class AppConstants {
  // App Information
  static const String appName = 'Tunisian Dating';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'A Tunisian dating app - private, local, and culturally respectful';
  
  // Country & Phone
  static const String tunisianCountryCode = '+216';
  static const String tunisianIsoCode = 'TN';
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String matchesCollection = 'matches';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';
  static const String reportsCollection = 'reports';
  static const String eventsCollection = 'events';
  
  // Storage Paths
  static const String profileImagesPath = 'profile_images';
  static const String chatImagesPath = 'chat_images';
  static const String voiceMessagesPath = 'voice_messages';
  
  // Shared Preferences Keys
  static const String userIdKey = 'user_id';
  static const String isFirstTimeKey = 'is_first_time';
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String locationPermissionKey = 'location_permission';
  
  // App Limits
  static const int maxProfileImages = 6;
  static const int maxBioLength = 500;
  static const int minAge = 18;
  static const int maxAge = 65;
  static const int maxDistanceKm = 100;
  static const int dailyLikesLimit = 50; // Free users
  static const int premiumDailyLikesLimit = 200;
  
  // Subscription Plans
  static const String basicPlanId = 'basic_monthly';
  static const String premiumPlanId = 'premium_monthly';
  static const String seriousPlanId = 'serious_monthly';
  
  // Pricing (in Tunisian Dinars)
  static const double basicPlanPrice = 15.0; // TND
  static const double premiumPlanPrice = 25.0; // TND
  static const double seriousPlanPrice = 35.0; // TND
  
  // AI Services Pricing
  static const double aiProfileHelpPrice = 3.0; // TND
  static const double photoQualityCheckPrice = 1.0; // TND
  
  // Cultural & Regional
  static const List<String> tunisianGovernorates = [
    'Tunis',
    'Ariana',
    'Ben Arous',
    'Manouba',
    'Nabeul',
    'Zaghouan',
    'Bizerte',
    'Béja',
    'Jendouba',
    'Kef',
    'Siliana',
    'Kairouan',
    'Kasserine',
    'Sidi Bouzid',
    'Sousse',
    'Monastir',
    'Mahdia',
    'Sfax',
    'Gafsa',
    'Tozeur',
    'Kebili',
    'Gabès',
    'Medenine',
    'Tataouine',
  ];
  
  // Interest Categories
  static const List<String> interestCategories = [
    'Sports',
    'Music',
    'Travel',
    'Food',
    'Movies',
    'Books',
    'Art',
    'Technology',
    'Fashion',
    'Photography',
    'Fitness',
    'Gaming',
    'Cooking',
    'Dancing',
    'Religion',
    'Family',
    'Career',
    'Education',
    'Volunteering',
    'Nature',
  ];
  
  // Relationship Goals
  static const List<String> relationshipGoals = [
    'Serious Relationship',
    'Marriage',
    'Friendship',
    'Casual Dating',
    'Something Casual',
    'Not Sure Yet',
  ];
  
  // Education Levels
  static const List<String> educationLevels = [
    'High School',
    'Some College',
    'Bachelor\'s Degree',
    'Master\'s Degree',
    'PhD',
    'Trade School',
    'Other',
  ];
  
  // Languages
  static const List<String> supportedLanguages = [
    'Arabic',
    'French',
    'English',
    'Tunisian Arabic (Darja)',
  ];
  
  // Default Values
  static const String defaultLanguage = 'ar';
  static const int defaultMaxDistance = 50;
  static const int defaultMinAge = 22;
  static const int defaultMaxAge = 35;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);
  
  // Network
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  
  // Chat
  static const int maxMessageLength = 1000;
  static const Duration voiceMessageMaxDuration = Duration(minutes: 2);
  static const int maxImageSizeMB = 10;
  
  // Verification
  static const Duration otpTimeout = Duration(minutes: 2);
  static const int otpLength = 6;
  
  // Privacy
  static const Duration blurPhotoTimeout = Duration(hours: 24); // After match
  static const int maxReportsPerUser = 5;
  
  // Premium Features
  static const List<String> premiumFeatures = [
    'See who liked you',
    'Unlimited likes',
    'Undo swipes',
    'Boost profile',
    'Travel mode',
    'Advanced filters',
    'Read receipts',
    'Priority support',
  ];
  
  // Serious Mode Features
  static const List<String> seriousModeFeatures = [
    'Verified profiles only',
    'Marriage-focused matching',
    'Family background info',
    'Career verification',
    'Enhanced privacy',
    'Relationship counselor access',
  ];
}

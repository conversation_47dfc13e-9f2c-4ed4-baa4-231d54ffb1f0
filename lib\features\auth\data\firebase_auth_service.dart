import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class FirebaseAuthService {
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  
  String? _verificationId;
  int? _resendToken;
  
  // Get current user
  User? get currentUser => _firebaseAuth.currentUser;
  
  // Stream of auth state changes
  Stream<User?> get authStateChanges => _firebaseAuth.authStateChanges();
  
  // Send SMS verification code
  Future<bool> sendVerificationCode({
    required String phoneNumber,
    required Function(String verificationId) onCodeSent,
    required Function(String error) onError,
    Function(PhoneAuthCredential credential)? onAutoVerification,
  }) async {
    try {
      await _firebaseAuth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification (Android only)
          if (onAutoVerification != null) {
            onAutoVerification(credential);
          } else {
            // Automatically sign in if auto-verification happens
            await _signInWithCredential(credential);
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String errorMessage = _getErrorMessage(e);
          onError(errorMessage);
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          _resendToken = resendToken;
          onCodeSent(verificationId);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
        timeout: const Duration(seconds: 60),
        forceResendingToken: _resendToken,
      );
      return true;
    } catch (e) {
      onError('Failed to send verification code: ${e.toString()}');
      return false;
    }
  }
  
  // Verify OTP code
  Future<User?> verifyOTP({
    required String otpCode,
    String? verificationId,
  }) async {
    try {
      final String vId = verificationId ?? _verificationId ?? '';
      if (vId.isEmpty) {
        throw Exception('Verification ID not found. Please request a new code.');
      }
      
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: vId,
        smsCode: otpCode,
      );
      
      return await _signInWithCredential(credential);
    } catch (e) {
      throw Exception('Invalid verification code: ${e.toString()}');
    }
  }
  
  // Sign in with credential
  Future<User?> _signInWithCredential(PhoneAuthCredential credential) async {
    try {
      UserCredential userCredential = await _firebaseAuth.signInWithCredential(credential);
      return userCredential.user;
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }
  
  // Resend verification code
  Future<bool> resendVerificationCode({
    required String phoneNumber,
    required Function(String verificationId) onCodeSent,
    required Function(String error) onError,
  }) async {
    return await sendVerificationCode(
      phoneNumber: phoneNumber,
      onCodeSent: onCodeSent,
      onError: onError,
    );
  }
  
  // Sign out
  Future<void> signOut() async {
    await _firebaseAuth.signOut();
  }
  
  // Delete account
  Future<void> deleteAccount() async {
    User? user = currentUser;
    if (user != null) {
      await user.delete();
    }
  }
  
  // Get error message from FirebaseAuthException
  String _getErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'invalid-phone-number':
        return 'The phone number format is invalid. Please check and try again.';
      case 'too-many-requests':
        return 'Too many requests. Please try again later.';
      case 'operation-not-allowed':
        return 'Phone authentication is not enabled. Please contact support.';
      case 'quota-exceeded':
        return 'SMS quota exceeded. Please try again later.';
      case 'invalid-verification-code':
        return 'The verification code is invalid. Please check and try again.';
      case 'invalid-verification-id':
        return 'The verification session has expired. Please request a new code.';
      case 'session-expired':
        return 'The verification session has expired. Please request a new code.';
      case 'credential-already-in-use':
        return 'This phone number is already associated with another account.';
      case 'user-disabled':
        return 'This account has been disabled. Please contact support.';
      case 'network-request-failed':
        return 'Network error. Please check your internet connection and try again.';
      default:
        if (kDebugMode) {
          print('Firebase Auth Error: ${e.code} - ${e.message}');
        }
        return 'An error occurred during verification. Please try again.';
    }
  }
  
  // Check if phone number is valid for Tunisia
  bool isValidTunisianPhoneNumber(String phoneNumber) {
    // Remove country code if present
    String number = phoneNumber.replaceAll('+216', '').replaceAll(' ', '');
    
    // Check if it's 8 digits and starts with valid prefixes
    if (number.length != 8) return false;
    
    // Valid Tunisian mobile prefixes: 2, 3, 4, 5, 7, 9
    // Valid landline prefixes: 7 (but we focus on mobile for dating app)
    RegExp tunisianMobileRegex = RegExp(r'^[23459][0-9]{7}$');
    return tunisianMobileRegex.hasMatch(number);
  }
  
  // Format phone number for Firebase (with country code)
  String formatPhoneNumber(String phoneNumber, String countryCode) {
    // Remove any existing country code and spaces
    String cleanNumber = phoneNumber
        .replaceAll('+216', '')
        .replaceAll('+', '')
        .replaceAll(' ', '')
        .replaceAll('-', '');
    
    // Add the country code
    return '$countryCode$cleanNumber';
  }
  
  // Get verification ID (for testing purposes)
  String? get verificationId => _verificationId;
  
  // Clear verification data
  void clearVerificationData() {
    _verificationId = null;
    _resendToken = null;
  }
}

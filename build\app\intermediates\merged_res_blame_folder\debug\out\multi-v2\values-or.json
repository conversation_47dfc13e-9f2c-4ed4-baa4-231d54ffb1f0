{"logs": [{"outputFile": "tn.dating.tunisian_dating_app-mergeDebugResources-49:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5ecd5a1cc2195013671502cd2470e636\\transformed\\browser-1.4.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,270,383", "endColumns": "109,104,112,108", "endOffsets": "160,265,378,487"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6163,6362,6467,6580", "endColumns": "109,104,112,108", "endOffsets": "6268,6462,6575,6684"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8470c89ba64407e2dd527ce11792b982\\transformed\\jetified-play-services-base-18.1.0\\res\\values-or\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,300,457,585,698,849,980,1090,1196,1359,1468,1625,1754,1900,2053,2114,2182", "endColumns": "106,156,127,112,150,130,109,105,162,108,156,128,145,152,60,67,82", "endOffsets": "299,456,584,697,848,979,1089,1195,1358,1467,1624,1753,1899,2052,2113,2181,2264"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3809,3920,4081,4213,4330,4485,4620,4734,4984,5151,5264,5425,5558,5708,5865,5930,6002", "endColumns": "110,160,131,116,154,134,113,109,166,112,160,132,149,156,64,71,86", "endOffsets": "3915,4076,4208,4325,4480,4615,4729,4839,5146,5259,5420,5553,5703,5860,5925,5997,6084"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,268,348,487,656,737", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "174,263,343,482,651,732,814"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6089,6273,6689,6769,7092,7261,7342", "endColumns": "73,88,79,138,168,80,81", "endOffsets": "6158,6357,6764,6903,7256,7337,7419"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb4aceea626ef6ec2db1cf5b297099f2\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4844", "endColumns": "139", "endOffsets": "4979"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\858939ab556fe096523710f298b68127\\transformed\\appcompat-1.3.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,906,997,1090,1186,1281,1381,1474,1569,1665,1756,1846,1935,2045,2149,2248,2359,2463,2581,2744,2850", "endColumns": "118,109,106,85,103,119,78,75,90,92,95,94,99,92,94,95,90,89,88,109,103,98,110,103,117,162,105,82", "endOffsets": "219,329,436,522,626,746,825,901,992,1085,1181,1276,1376,1469,1564,1660,1751,1841,1930,2040,2144,2243,2354,2458,2576,2739,2845,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,906,997,1090,1186,1281,1381,1474,1569,1665,1756,1846,1935,2045,2149,2248,2359,2463,2581,2744,6908", "endColumns": "118,109,106,85,103,119,78,75,90,92,95,94,99,92,94,95,90,89,88,109,103,98,110,103,117,162,105,82", "endOffsets": "219,329,436,522,626,746,825,901,992,1085,1181,1276,1376,1469,1564,1660,1751,1841,1930,2040,2144,2243,2354,2458,2576,2739,2845,6986"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\24c1e520e2b1e7ad04d8500a33fb9ad9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,111", "endOffsets": "162,274"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2850,2962", "endColumns": "111,111", "endOffsets": "2957,3069"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3074,3177,3279,3382,3487,3588,3690,6991", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3172,3274,3377,3482,3583,3685,3804,7087"}}]}]}
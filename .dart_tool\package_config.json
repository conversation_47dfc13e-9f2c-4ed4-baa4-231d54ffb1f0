{"configVersion": 2, "packages": [{"name": "_flutterfire_internals", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/_flutterfire_internals-1.3.58", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "args", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/async-2.11.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "boolean_selector", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/boolean_selector-2.1.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "characters", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/characters-1.3.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "clock", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/clock-1.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "cloud_firestore", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore-5.6.11", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_platform_interface-6.6.11", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "cloud_firestore_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cloud_firestore_web-4.4.11", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "collection", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/collection-1.18.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "country_code_picker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/country_code_picker-3.3.0", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "cupertino_icons", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "diacritic", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/diacritic-0.1.6", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fake_async", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/fake_async-1.3.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "ffi", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/ffi-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "file", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "firebase_auth", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth-5.6.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_platform_interface-7.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_auth_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_auth_web-5.15.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "firebase_core", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core-3.15.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_platform_interface-6.0.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "firebase_core_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/firebase_core_web-2.24.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "flutter_localizations", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_localizations", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter_riverpod", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_svg", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/flutter_svg-2.1.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "flutter_test", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "flutter_web_plugins", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Flutter/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "http", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/http_parser-4.0.2", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "intl", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/intl-0.19.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "leak_tracker", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker-10.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.5", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/lints-4.0.0", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "matcher", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/matcher-0.12.16+1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "material_color_utilities", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/meta-1.15.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "path", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path-1.9.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_parsing", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_parsing-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "petitparser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/petitparser-6.0.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "phone_numbers_parser", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/phone_numbers_parser-8.3.0", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pin_code_fields", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/pin_code_fields-8.0.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "platform", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "riverpod", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shared_preferences", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_android-2.4.7", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_foundation", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Flutter/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "source_span", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/source_span-1.10.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "stack_trace", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stack_trace-1.11.1", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "state_notifier", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/state_notifier-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stream_channel", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/stream_channel-2.1.2", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "string_scanner", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/string_scanner-1.2.0", "packageUri": "lib/", "languageVersion": "2.18"}, {"name": "term_glyph", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/term_glyph-1.2.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "test_api", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/test_api-0.7.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "typed_data", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_graphics", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics-1.1.18", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_codec", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_codec-1.1.13", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_graphics_compiler", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_graphics_compiler-1.1.16", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "vector_math", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/vm_service-14.2.5", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "web", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "xdg_directories", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xml", "rootUri": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache/hosted/pub.dev/xml-6.5.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "tunisian_dating_app", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.5"}], "generated": "2025-07-04T09:52:23.338861Z", "generator": "pub", "generatorVersion": "3.5.3", "flutterRoot": "file:///C:/Users/<USER>/AppData/Local/Flutter", "flutterVersion": "3.24.3", "pubCache": "file:///C:/Users/<USER>/AppData/Local/Pub/Cache"}
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidImageFile() {
      return request.resource.contentType.matches('image/.*') &&
             request.resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    // User profile photos
    match /users/{userId}/photos/{photoId} {
      // Allow read if:
      // 1. User is reading their own photos
      // 2. Any authenticated user (for viewing profiles)
      allow read: if isOwner(userId) || isAuthenticated();
      
      // Allow write only for the photo owner
      allow write: if isOwner(userId) && isValidImageFile();
      
      // Allow delete only for the photo owner
      allow delete: if isOwner(userId);
    }
    
    // User profile thumbnails (auto-generated)
    match /users/{userId}/thumbnails/{photoId} {
      // Allow read for any authenticated user
      allow read: if isAuthenticated();
      
      // Only allow system/cloud functions to write thumbnails
      allow write: if false;
      
      // Allow delete only for the photo owner
      allow delete: if isOwner(userId);
    }
    
    // Temporary uploads (for processing)
    match /temp/{userId}/{fileName} {
      // Allow read/write only for the owner
      allow read, write: if isOwner(userId) && isValidImageFile();
      
      // Allow delete for the owner
      allow delete: if isOwner(userId);
    }
    
    // Chat media (for future chat system)
    match /chat/{chatId}/{mediaId} {
      // Allow read if user is part of the chat
      // This would require checking Firestore for chat membership
      allow read: if isAuthenticated();
      
      // Allow write if user is part of the chat and file is valid
      allow write: if isAuthenticated() && 
                      (isValidImageFile() || 
                       request.resource.contentType.matches('video/.*')) &&
                      request.resource.size < 50 * 1024 * 1024; // 50MB for videos
      
      // Allow delete if user uploaded the media
      allow delete: if isAuthenticated();
    }
    
    // Public assets (app resources)
    match /public/{allPaths=**} {
      // Allow read for everyone
      allow read: if true;
      
      // No write access for users
      allow write: if false;
    }
    
    // Admin uploads
    match /admin/{allPaths=**} {
      // Only allow access to verified admin users
      allow read, write: if isAuthenticated() && 
                            request.auth.token.admin == true;
    }
    
    // Default deny all other paths
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}

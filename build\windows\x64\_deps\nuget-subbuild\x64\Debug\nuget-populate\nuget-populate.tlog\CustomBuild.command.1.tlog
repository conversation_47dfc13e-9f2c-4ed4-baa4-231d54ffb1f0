^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/tmp/nuget-populate-mkdirs.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-mkdir"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-DOWNLOAD.RULE
setlocal
cd "C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/download-nuget-populate.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -P "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/verify-nuget-populate.cmake"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-download"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-UPDATE.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-update"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-patch"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-COPYFILE.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E copy_if_different "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget.exe" "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-src"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-copyfile"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-CONFIGURE.RULE
setlocal
cd "C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-configure"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-BUILD.RULE
setlocal
cd "C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-build"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-INSTALL.RULE
setlocal
cd "C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-install"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\8AE3CE00C5F5FEE85DC1AA2EB9DE4991\NUGET-POPULATE-TEST.RULE
setlocal
cd "C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-build"
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-test"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\DEE5A3C7B7534910FC6F2880A99070FC\NUGET-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E make_directory "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/Debug/nuget-populate-complete"
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E touch "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate-prefix/src/nuget-populate-stamp/Debug/nuget-populate-done"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\4398D089C06A70308B78252120ABC511\NUGET-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild" "-BC:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild" --check-stamp-file "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/CMakeFiles/generate.stamp"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

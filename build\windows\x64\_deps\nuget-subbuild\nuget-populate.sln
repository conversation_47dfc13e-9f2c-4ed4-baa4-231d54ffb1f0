﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{030E09D6-78BA-307F-B2CE-ABF360B8AA7A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{9DE7C5A9-20CD-3B96-BE74-A388070D72EE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nuget-populate", "ExternalProjectTargets\nuget-populate", "{6CA5BD4F-807F-399E-8E71-99C108CD1037}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{5EA38703-6B7D-3FB4-B965-EA95EF4E2F51}"
	ProjectSection(ProjectDependencies) = postProject
		{ED3C235A-8597-38B3-BB71-7E982A03629A} = {ED3C235A-8597-38B3-BB71-7E982A03629A}
		{AE7777BD-CC60-3BAD-B6B4-674582BC0DFE} = {AE7777BD-CC60-3BAD-B6B4-674582BC0DFE}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{ED3C235A-8597-38B3-BB71-7E982A03629A}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "nuget-populate", "nuget-populate.vcxproj", "{AE7777BD-CC60-3BAD-B6B4-674582BC0DFE}"
	ProjectSection(ProjectDependencies) = postProject
		{ED3C235A-8597-38B3-BB71-7E982A03629A} = {ED3C235A-8597-38B3-BB71-7E982A03629A}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5EA38703-6B7D-3FB4-B965-EA95EF4E2F51}.Debug|x64.ActiveCfg = Debug|x64
		{ED3C235A-8597-38B3-BB71-7E982A03629A}.Debug|x64.ActiveCfg = Debug|x64
		{ED3C235A-8597-38B3-BB71-7E982A03629A}.Debug|x64.Build.0 = Debug|x64
		{AE7777BD-CC60-3BAD-B6B4-674582BC0DFE}.Debug|x64.ActiveCfg = Debug|x64
		{AE7777BD-CC60-3BAD-B6B4-674582BC0DFE}.Debug|x64.Build.0 = Debug|x64
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{5EA38703-6B7D-3FB4-B965-EA95EF4E2F51} = {030E09D6-78BA-307F-B2CE-ABF360B8AA7A}
		{ED3C235A-8597-38B3-BB71-7E982A03629A} = {030E09D6-78BA-307F-B2CE-ABF360B8AA7A}
		{6CA5BD4F-807F-399E-8E71-99C108CD1037} = {9DE7C5A9-20CD-3B96-BE74-A388070D72EE}
		{AE7777BD-CC60-3BAD-B6B4-674582BC0DFE} = {6CA5BD4F-807F-399E-8E71-99C108CD1037}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3B32093F-57D3-32C4-891A-60B31454035F}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

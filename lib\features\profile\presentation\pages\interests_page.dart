import 'package:flutter/material.dart';
import 'privacy_settings_page.dart';
import '../../data/services/profile_service.dart';

class InterestsPage extends StatefulWidget {
  const InterestsPage({super.key});

  @override
  State<InterestsPage> createState() => _InterestsPageState();
}

class _InterestsPageState extends State<InterestsPage> {
  final Set<String> _selectedInterests = {};
  final Set<String> _selectedInterestedIn = {};
  int _minAge = 18;
  int _maxAge = 35;
  int _maxDistance = 50;
  bool _showOnlyVerified = false;
  bool _showDistance = true;
  bool _isLoading = false;

  // Available interests (culturally appropriate for Tunisia)
  final List<Map<String, dynamic>> _availableInterests = [
    {'name': 'Travel', 'icon': Icons.flight, 'nameAr': 'السفر'},
    {'name': 'Food', 'icon': Icons.restaurant, 'nameAr': 'الطعام'},
    {'name': 'Music', 'icon': Icons.music_note, 'nameAr': 'الموسيقى'},
    {'name': 'Sports', 'icon': Icons.sports_soccer, 'nameAr': 'الرياضة'},
    {'name': 'Reading', 'icon': Icons.book, 'nameAr': 'القراءة'},
    {'name': 'Movies', 'icon': Icons.movie, 'nameAr': 'الأفلام'},
    {'name': 'Photography', 'icon': Icons.camera_alt, 'nameAr': 'التصوير'},
    {'name': 'Art', 'icon': Icons.palette, 'nameAr': 'الفن'},
    {'name': 'Fitness', 'icon': Icons.fitness_center, 'nameAr': 'اللياقة'},
    {'name': 'Cooking', 'icon': Icons.kitchen, 'nameAr': 'الطبخ'},
    {'name': 'Nature', 'icon': Icons.nature, 'nameAr': 'الطبيعة'},
    {'name': 'Technology', 'icon': Icons.computer, 'nameAr': 'التكنولوجيا'},
    {'name': 'Fashion', 'icon': Icons.checkroom, 'nameAr': 'الموضة'},
    {'name': 'Gaming', 'icon': Icons.games, 'nameAr': 'الألعاب'},
    {'name': 'Dancing', 'icon': Icons.music_video, 'nameAr': 'الرقص'},
    {'name': 'Coffee', 'icon': Icons.local_cafe, 'nameAr': 'القهوة'},
    {'name': 'Beach', 'icon': Icons.beach_access, 'nameAr': 'الشاطئ'},
    {'name': 'History', 'icon': Icons.museum, 'nameAr': 'التاريخ'},
    {'name': 'Languages', 'icon': Icons.translate, 'nameAr': 'اللغات'},
    {
      'name': 'Volunteering',
      'icon': Icons.volunteer_activism,
      'nameAr': 'التطوع'
    },
  ];

  void _continueToNext() async {
    if (_selectedInterests.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select at least one interest'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_selectedInterestedIn.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select who you\'re interested in'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Save interests and preferences to Firebase
      await ProfileService().updateInterestsAndPreferences(
        interests: _selectedInterests.toList(),
        interestedIn: _selectedInterestedIn.toList(),
        minAge: _minAge,
        maxAge: _maxAge,
        maxDistance: _maxDistance,
        showOnlyVerified: _showOnlyVerified,
        showDistance: _showDistance,
      );

      if (mounted) {
        // Navigate to privacy settings page
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const PrivacySettingsPage(),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving preferences: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Interests & Preferences'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: const Color(0xFFE74C3C),
        actions: [
          TextButton(
            onPressed: () {
              // Skip this step
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const PrivacySettingsPage(),
                ),
              );
            },
            child: const Text(
              'Skip',
              style: TextStyle(
                color: Color(0xFFE74C3C),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: 0.75, // 3/4 steps completed
                backgroundColor: Colors.grey[300],
                valueColor:
                    const AlwaysStoppedAnimation<Color>(Color(0xFFE74C3C)),
              ),

              const SizedBox(height: 32),

              Text(
                'Your Interests',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
              ),

              const SizedBox(height: 8),

              Text(
                'Select what you love to do. This helps us find better matches for you.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),

              const SizedBox(height: 24),

              // Interests grid
              Wrap(
                spacing: 12,
                runSpacing: 12,
                children: _availableInterests.map((interest) {
                  final isSelected =
                      _selectedInterests.contains(interest['name']);
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        if (isSelected) {
                          _selectedInterests.remove(interest['name']);
                        } else {
                          _selectedInterests.add(interest['name']);
                        }
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        color:
                            isSelected ? const Color(0xFFE74C3C) : Colors.white,
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isSelected
                              ? const Color(0xFFE74C3C)
                              : Colors.grey[300]!,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.05),
                            blurRadius: 5,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            interest['icon'],
                            size: 20,
                            color: isSelected
                                ? Colors.white
                                : const Color(0xFFE74C3C),
                          ),
                          const SizedBox(width: 8),
                          Text(
                            interest['name'],
                            style: TextStyle(
                              color: isSelected ? Colors.white : Colors.black87,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 40),

              // Dating preferences
              Text(
                'Dating Preferences',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
              ),

              const SizedBox(height: 24),

              // Interested in
              Text(
                'I\'m interested in:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),

              const SizedBox(height: 12),

              Row(
                children: [
                  _buildInterestedInChip('Men', 'الرجال'),
                  const SizedBox(width: 12),
                  _buildInterestedInChip('Women', 'النساء'),
                ],
              ),

              const SizedBox(height: 24),

              // Age range
              Text(
                'Age Range: $_minAge - $_maxAge years',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),

              const SizedBox(height: 12),

              RangeSlider(
                values: RangeValues(_minAge.toDouble(), _maxAge.toDouble()),
                min: 18,
                max: 60,
                divisions: 42,
                activeColor: const Color(0xFFE74C3C),
                inactiveColor: Colors.grey[300],
                labels: RangeLabels(_minAge.toString(), _maxAge.toString()),
                onChanged: (values) {
                  setState(() {
                    _minAge = values.start.round();
                    _maxAge = values.end.round();
                  });
                },
              ),

              const SizedBox(height: 24),

              // Distance
              Text(
                'Maximum Distance: $_maxDistance km',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),

              const SizedBox(height: 12),

              Slider(
                value: _maxDistance.toDouble(),
                min: 5,
                max: 200,
                divisions: 39,
                activeColor: const Color(0xFFE74C3C),
                inactiveColor: Colors.grey[300],
                label: '$_maxDistance km',
                onChanged: (value) {
                  setState(() {
                    _maxDistance = value.round();
                  });
                },
              ),

              const SizedBox(height: 24),

              // Additional preferences
              SwitchListTile(
                title: const Text('Show only verified profiles'),
                subtitle:
                    const Text('Only see profiles with verified phone numbers'),
                value: _showOnlyVerified,
                activeColor: const Color(0xFFE74C3C),
                onChanged: (value) {
                  setState(() {
                    _showOnlyVerified = value;
                  });
                },
              ),

              SwitchListTile(
                title: const Text('Show distance'),
                subtitle: const Text('Display distance to other users'),
                value: _showDistance,
                activeColor: const Color(0xFFE74C3C),
                onChanged: (value) {
                  setState(() {
                    _showDistance = value;
                  });
                },
              ),

              const SizedBox(height: 32),

              // Continue Button
              ElevatedButton(
                onPressed: _isLoading ? null : _continueToNext,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Continue',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInterestedInChip(String label, String labelAr) {
    final isSelected = _selectedInterestedIn.contains(label);
    return GestureDetector(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedInterestedIn.remove(label);
          } else {
            _selectedInterestedIn.add(label);
          }
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFE74C3C) : Colors.white,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected ? const Color(0xFFE74C3C) : Colors.grey[300]!,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 5,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.black87,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

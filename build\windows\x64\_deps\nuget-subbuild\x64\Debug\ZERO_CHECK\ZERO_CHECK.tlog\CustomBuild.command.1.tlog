^C:\USERS\<USER>\ONEDRIVE\DESKTOP\DATING APP\BUILD\WINDOWS\X64\_DEPS\NUGET-SUBBUILD\CMAKEFILES\4398D089C06A70308B78252120ABC511\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" "-SC:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild" "-BC:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild" --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file "C:/Users/<USER>/OneDrive/Desktop/dating app/build/windows/x64/_deps/nuget-subbuild/nuget-populate.sln"
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

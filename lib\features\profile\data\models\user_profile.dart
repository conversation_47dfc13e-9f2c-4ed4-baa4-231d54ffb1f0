import 'package:cloud_firestore/cloud_firestore.dart';

class UserProfile {
  final String userId;
  final String phoneNumber;
  final String? firstName;
  final String? lastName;
  final DateTime? dateOfBirth;
  final String? bio;
  final String? location;
  final String? city;
  final String? governorate; // Tunisian administrative division
  final List<String> photos;
  final String? profilePhotoUrl;
  final List<String> interests;
  final UserPreferences preferences;
  final PrivacySettings privacy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isProfileComplete;
  final bool isVerified;

  UserProfile({
    required this.userId,
    required this.phoneNumber,
    this.firstName,
    this.lastName,
    this.dateOfBirth,
    this.bio,
    this.location,
    this.city,
    this.governorate,
    this.photos = const [],
    this.profilePhotoUrl,
    this.interests = const [],
    required this.preferences,
    required this.privacy,
    required this.createdAt,
    required this.updatedAt,
    this.isProfileComplete = false,
    this.isVerified = false,
  });

  // Calculate age from date of birth
  int? get age {
    if (dateOfBirth == null) return null;
    final now = DateTime.now();
    int age = now.year - dateOfBirth!.year;
    if (now.month < dateOfBirth!.month || 
        (now.month == dateOfBirth!.month && now.day < dateOfBirth!.day)) {
      age--;
    }
    return age;
  }

  // Get full name
  String get fullName {
    if (firstName == null && lastName == null) return 'User';
    return '${firstName ?? ''} ${lastName ?? ''}'.trim();
  }

  // Check if profile has minimum required information
  bool get hasMinimumInfo {
    return firstName != null && 
           firstName!.isNotEmpty && 
           dateOfBirth != null && 
           photos.isNotEmpty;
  }

  // Convert to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'phoneNumber': phoneNumber,
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'bio': bio,
      'location': location,
      'city': city,
      'governorate': governorate,
      'photos': photos,
      'profilePhotoUrl': profilePhotoUrl,
      'interests': interests,
      'preferences': preferences.toMap(),
      'privacy': privacy.toMap(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isProfileComplete': isProfileComplete,
      'isVerified': isVerified,
    };
  }

  // Create from Firestore document
  factory UserProfile.fromMap(Map<String, dynamic> map) {
    return UserProfile(
      userId: map['userId'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      firstName: map['firstName'],
      lastName: map['lastName'],
      dateOfBirth: map['dateOfBirth'] != null 
          ? DateTime.parse(map['dateOfBirth']) 
          : null,
      bio: map['bio'],
      location: map['location'],
      city: map['city'],
      governorate: map['governorate'],
      photos: List<String>.from(map['photos'] ?? []),
      profilePhotoUrl: map['profilePhotoUrl'],
      interests: List<String>.from(map['interests'] ?? []),
      preferences: UserPreferences.fromMap(map['preferences'] ?? {}),
      privacy: PrivacySettings.fromMap(map['privacy'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      isProfileComplete: map['isProfileComplete'] ?? false,
      isVerified: map['isVerified'] ?? false,
    );
  }

  // Create from Firestore DocumentSnapshot
  factory UserProfile.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return UserProfile.fromMap(data);
  }

  // Copy with method for updates
  UserProfile copyWith({
    String? firstName,
    String? lastName,
    DateTime? dateOfBirth,
    String? bio,
    String? location,
    String? city,
    String? governorate,
    List<String>? photos,
    String? profilePhotoUrl,
    List<String>? interests,
    UserPreferences? preferences,
    PrivacySettings? privacy,
    DateTime? updatedAt,
    bool? isProfileComplete,
    bool? isVerified,
  }) {
    return UserProfile(
      userId: userId,
      phoneNumber: phoneNumber,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      bio: bio ?? this.bio,
      location: location ?? this.location,
      city: city ?? this.city,
      governorate: governorate ?? this.governorate,
      photos: photos ?? this.photos,
      profilePhotoUrl: profilePhotoUrl ?? this.profilePhotoUrl,
      interests: interests ?? this.interests,
      preferences: preferences ?? this.preferences,
      privacy: privacy ?? this.privacy,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      isProfileComplete: isProfileComplete ?? this.isProfileComplete,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}

// User dating preferences
class UserPreferences {
  final int minAge;
  final int maxAge;
  final int maxDistance; // in kilometers
  final List<String> interestedIn; // ['men', 'women', 'both']
  final bool showOnlyVerified;
  final bool showDistance;

  UserPreferences({
    this.minAge = 18,
    this.maxAge = 35,
    this.maxDistance = 50,
    this.interestedIn = const ['men'],
    this.showOnlyVerified = false,
    this.showDistance = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'minAge': minAge,
      'maxAge': maxAge,
      'maxDistance': maxDistance,
      'interestedIn': interestedIn,
      'showOnlyVerified': showOnlyVerified,
      'showDistance': showDistance,
    };
  }

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      minAge: map['minAge'] ?? 18,
      maxAge: map['maxAge'] ?? 35,
      maxDistance: map['maxDistance'] ?? 50,
      interestedIn: List<String>.from(map['interestedIn'] ?? ['men']),
      showOnlyVerified: map['showOnlyVerified'] ?? false,
      showDistance: map['showDistance'] ?? true,
    );
  }
}

// Privacy settings
class PrivacySettings {
  final bool blurPhotos;
  final bool hideAge;
  final bool hideDistance;
  final bool hideLastSeen;
  final bool onlyMatchedCanMessage;
  final bool showOnlineStatus;

  PrivacySettings({
    this.blurPhotos = false,
    this.hideAge = false,
    this.hideDistance = false,
    this.hideLastSeen = false,
    this.onlyMatchedCanMessage = true,
    this.showOnlineStatus = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'blurPhotos': blurPhotos,
      'hideAge': hideAge,
      'hideDistance': hideDistance,
      'hideLastSeen': hideLastSeen,
      'onlyMatchedCanMessage': onlyMatchedCanMessage,
      'showOnlineStatus': showOnlineStatus,
    };
  }

  factory PrivacySettings.fromMap(Map<String, dynamic> map) {
    return PrivacySettings(
      blurPhotos: map['blurPhotos'] ?? false,
      hideAge: map['hideAge'] ?? false,
      hideDistance: map['hideDistance'] ?? false,
      hideLastSeen: map['hideLastSeen'] ?? false,
      onlyMatchedCanMessage: map['onlyMatchedCanMessage'] ?? true,
      showOnlineStatus: map['showOnlineStatus'] ?? true,
    );
  }
}

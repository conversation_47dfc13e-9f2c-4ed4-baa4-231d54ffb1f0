﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-copyfile.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\8ae3ce00c5f5fee85dc1aa2eb9de4991\nuget-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\dee5a3c7b7534910fc6f2880a99070fc\nuget-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\4398d089c06a70308b78252120abc511\nuget-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\OneDrive\Desktop\dating app\build\windows\x64\_deps\nuget-subbuild\CMakeFiles\nuget-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{ACE6E6DE-A479-391C-A6B0-883D5295611D}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>

rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUserProfile() {
      return request.resource.data.keys().hasAll(['userId', 'phoneNumber', 'createdAt', 'updatedAt']) &&
             request.resource.data.userId == request.auth.uid &&
             request.resource.data.phoneNumber is string &&
             request.resource.data.createdAt is timestamp &&
             request.resource.data.updatedAt is timestamp;
    }
    
    function isValidProfileUpdate() {
      return request.resource.data.diff(resource.data).affectedKeys()
        .hasOnly(['firstName', 'lastName', 'dateOfBirth', 'bio', 'city', 'governorate', 
                 'photos', 'profilePhotoUrl', 'interests', 'preferences', 'privacy', 
                 'updatedAt', 'isProfileComplete', 'lastSeen']) &&
             request.resource.data.userId == resource.data.userId &&
             request.resource.data.phoneNumber == resource.data.phoneNumber &&
             request.resource.data.createdAt == resource.data.createdAt;
    }
    
    // Users collection rules
    match /users/{userId} {
      // Allow read if:
      // 1. User is reading their own profile
      // 2. User is authenticated and the profile is complete (for matching)
      allow read: if isOwner(userId) || 
                     (isAuthenticated() && 
                      resource.data.isProfileComplete == true);
      
      // Allow create only for the authenticated user's own profile
      allow create: if isOwner(userId) && isValidUserProfile();
      
      // Allow update only for the authenticated user's own profile
      allow update: if isOwner(userId) && isValidProfileUpdate();
      
      // Allow delete only for the authenticated user's own profile
      allow delete: if isOwner(userId);
    }
    
    // Matches collection (for future matching system)
    match /matches/{matchId} {
      // Allow read if user is part of the match
      allow read: if isAuthenticated() && 
                     (resource.data.user1Id == request.auth.uid || 
                      resource.data.user2Id == request.auth.uid);
      
      // Allow create if user is creating a match they're part of
      allow create: if isAuthenticated() && 
                       (request.resource.data.user1Id == request.auth.uid || 
                        request.resource.data.user2Id == request.auth.uid);
      
      // Allow update if user is part of the match
      allow update: if isAuthenticated() && 
                       (resource.data.user1Id == request.auth.uid || 
                        resource.data.user2Id == request.auth.uid);
    }
    
    // Messages collection (for future chat system)
    match /messages/{messageId} {
      // Allow read if user is sender or receiver
      allow read: if isAuthenticated() && 
                     (resource.data.senderId == request.auth.uid || 
                      resource.data.receiverId == request.auth.uid);
      
      // Allow create if user is the sender
      allow create: if isAuthenticated() && 
                       request.resource.data.senderId == request.auth.uid;
      
      // Allow update if user is the sender (for message status updates)
      allow update: if isAuthenticated() && 
                       resource.data.senderId == request.auth.uid;
    }
    
    // Reports collection (for safety and moderation)
    match /reports/{reportId} {
      // Allow read only for the reporter
      allow read: if isAuthenticated() && 
                     resource.data.reporterId == request.auth.uid;
      
      // Allow create if user is the reporter
      allow create: if isAuthenticated() && 
                       request.resource.data.reporterId == request.auth.uid;
    }
    
    // Admin collection (for app administrators)
    match /admin/{document=**} {
      // Only allow access to verified admin users
      // This would be managed through custom claims
      allow read, write: if isAuthenticated() && 
                            request.auth.token.admin == true;
    }
    
    // Analytics collection (for app usage analytics)
    match /analytics/{document=**} {
      // Allow write for authenticated users (for usage tracking)
      allow write: if isAuthenticated();
      
      // No read access for regular users
      allow read: if false;
    }
  }
}

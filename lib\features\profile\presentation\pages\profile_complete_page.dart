import 'package:flutter/material.dart';

class ProfileCompleteePage extends StatelessWidget {
  const ProfileCompleteePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const Spacer(),
              
              // Success animation placeholder
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: const Color(0xFFE74C3C).withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.favorite,
                  size: 60,
                  color: Color(0xFFE74C3C),
                ),
              ),
              
              const SizedBox(height: 32),
              
              Text(
                'Profile Complete!',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 16),
              
              Text(
                'Your profile is now ready to help you find meaningful connections in Tunisia.',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Colors.grey[600],
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              
              const SizedBox(height: 40),
              
              // Profile summary
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    _buildSummaryItem(
                      icon: Icons.person,
                      title: 'Basic Information',
                      subtitle: 'Name, age, and location added',
                      isComplete: true,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildSummaryItem(
                      icon: Icons.photo_camera,
                      title: 'Photos',
                      subtitle: 'Profile photos uploaded',
                      isComplete: true,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildSummaryItem(
                      icon: Icons.favorite,
                      title: 'Interests & Preferences',
                      subtitle: 'Dating preferences set',
                      isComplete: true,
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildSummaryItem(
                      icon: Icons.security,
                      title: 'Privacy Settings',
                      subtitle: 'Privacy controls configured',
                      isComplete: true,
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // Tips for success
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.lightbulb,
                          color: Colors.blue,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Tips for Success',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontWeight: FontWeight.w600,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• Be genuine and authentic in your interactions\n'
                      '• Respect cultural values and traditions\n'
                      '• Take time to get to know potential matches\n'
                      '• Report any inappropriate behavior',
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontSize: 14,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // Start exploring button
              ElevatedButton(
                onPressed: () {
                  // TODO: Navigate to main app (matching interface)
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                      builder: (context) => const MainAppPage(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'Start Exploring',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Edit profile later
              TextButton(
                onPressed: () {
                  // TODO: Navigate to profile edit page
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('You can edit your profile anytime in settings'),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
                child: Text(
                  'Edit Profile Later',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required bool isComplete,
  }) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isComplete 
                ? const Color(0xFFE74C3C).withOpacity(0.1)
                : Colors.grey.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            isComplete ? Icons.check : icon,
            color: isComplete ? const Color(0xFFE74C3C) : Colors.grey,
            size: 20,
          ),
        ),
        
        const SizedBox(width: 16),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        
        if (isComplete)
          const Icon(
            Icons.check_circle,
            color: Color(0xFFE74C3C),
            size: 20,
          ),
      ],
    );
  }
}

// Placeholder for MainAppPage
class MainAppPage extends StatelessWidget {
  const MainAppPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Tunisian Dating'),
        backgroundColor: const Color(0xFFE74C3C),
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite,
              size: 80,
              color: Color(0xFFE74C3C),
            ),
            SizedBox(height: 24),
            Text(
              'Welcome to Tunisian Dating!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Main app features coming soon...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

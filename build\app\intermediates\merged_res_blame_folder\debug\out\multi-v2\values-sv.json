{"logs": [{"outputFile": "tn.dating.tunisian_dating_app-mergeDebugResources-47:/values-sv/values-sv.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb4aceea626ef6ec2db1cf5b297099f2\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sv\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "143", "endOffsets": "338"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4725", "endColumns": "147", "endOffsets": "4868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8470c89ba64407e2dd527ce11792b982\\transformed\\jetified-play-services-base-18.1.0\\res\\values-sv\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,449,572,678,815,936,1055,1155,1299,1403,1561,1685,1835,1987,2049,2108", "endColumns": "102,152,122,105,136,120,118,99,143,103,157,123,149,151,61,58,74", "endOffsets": "295,448,571,677,814,935,1054,1154,1298,1402,1560,1684,1834,1986,2048,2107,2182"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3731,3838,3995,4122,4232,4373,4498,4621,4873,5021,5129,5291,5419,5573,5729,5795,5858", "endColumns": "106,156,126,109,140,124,122,103,147,107,161,127,153,155,65,62,78", "endOffsets": "3833,3990,4117,4227,4368,4493,4616,4720,5016,5124,5286,5414,5568,5724,5790,5853,5932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\24c1e520e2b1e7ad04d8500a33fb9ad9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,112", "endOffsets": "162,275"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2778,2890", "endColumns": "111,112", "endOffsets": "2885,2998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3a04377ab98aaf9189ccddcb951bae89\\transformed\\appcompat-1.1.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,874,967,1061,1156,1250,1353,1448,1545,1643,1739,1832,1911,2017,2116,2212,2317,2420,2522,2676,2778", "endColumns": "102,102,110,83,101,112,76,75,92,93,94,93,102,94,96,97,95,92,78,105,98,95,104,102,101,153,101,78", "endOffsets": "203,306,417,501,603,716,793,869,962,1056,1151,1245,1348,1443,1540,1638,1734,1827,1906,2012,2111,2207,2312,2415,2517,2671,2773,2852"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,422,506,608,721,798,874,967,1061,1156,1250,1353,1448,1545,1643,1739,1832,1911,2017,2116,2212,2317,2420,2522,2676,6722", "endColumns": "102,102,110,83,101,112,76,75,92,93,94,93,102,94,96,97,95,92,78,105,98,95,104,102,101,153,101,78", "endOffsets": "203,306,417,501,603,716,793,869,962,1056,1151,1245,1348,1443,1540,1638,1734,1827,1906,2012,2111,2207,2312,2415,2517,2671,2773,6796"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,449,557,662,783", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "145,247,345,444,552,657,778,879"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3003,3098,3200,3298,3397,3505,3610,6801", "endColumns": "94,101,97,98,107,104,120,100", "endOffsets": "3093,3195,3293,3392,3500,3605,3726,6897"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,264,342,479,648,727", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "171,259,337,474,643,722,798"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "5937,6108,6507,6585,6902,7071,7150", "endColumns": "70,87,77,136,168,78,75", "endOffsets": "6003,6191,6580,6717,7066,7145,7221"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5ecd5a1cc2195013671502cd2470e636\\transformed\\browser-1.4.0\\res\\values-sv\\values-sv.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,368", "endColumns": "99,99,112,97", "endOffsets": "150,250,363,461"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6008,6196,6296,6409", "endColumns": "99,99,112,97", "endOffsets": "6103,6291,6404,6502"}}]}]}
1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="tn.dating.tunisian_dating_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\96d3ffafa270f328b6b1595e0942b698\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\96d3ffafa270f328b6b1595e0942b698\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="tn.dating.tunisian_dating_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="tn.dating.tunisian_dating_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@mipmap/ic_launcher"
46        android:label="tunisian_dating_app" >
47        <activity
48            android:name="tn.dating.tunisian_dating_app.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <service
81-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
82            android:name="com.google.firebase.components.ComponentDiscoveryService"
82-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
83            android:directBootAware="true"
83-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
84            android:exported="false" >
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
85            <meta-data
85-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
86                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
86-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
88            <meta-data
88-->[:firebase_auth] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
89                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
89-->[:firebase_auth] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[:firebase_auth] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
91            <meta-data
91-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
92                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
92-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
94            <meta-data
94-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
95                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
97            <meta-data
97-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
98                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
98-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
100            <meta-data
100-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
101                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
101-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
103            <meta-data
103-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\de1f68a58916d382e87cebe6de5ae38f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
104                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
104-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\de1f68a58916d382e87cebe6de5ae38f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\de1f68a58916d382e87cebe6de5ae38f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
106            <meta-data
106-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
107                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
107-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
109        </service>
110
111        <activity
111-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
112            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
112-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
113            android:excludeFromRecents="true"
113-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
114            android:exported="true"
114-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
115            android:launchMode="singleTask"
115-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
116            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
116-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
117            <intent-filter>
117-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
118                <action android:name="android.intent.action.VIEW" />
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
118-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
119
120                <category android:name="android.intent.category.DEFAULT" />
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
120-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
121                <category android:name="android.intent.category.BROWSABLE" />
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
121-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
122
123                <data
123-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:13-50
124                    android:host="firebase.auth"
125                    android:path="/"
126                    android:scheme="genericidp" />
127            </intent-filter>
128        </activity>
129        <activity
129-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
130            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
130-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
131            android:excludeFromRecents="true"
131-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
132            android:exported="true"
132-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
133            android:launchMode="singleTask"
133-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
134            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
134-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
135            <intent-filter>
135-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
136                <action android:name="android.intent.action.VIEW" />
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
137
138                <category android:name="android.intent.category.DEFAULT" />
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
139                <category android:name="android.intent.category.BROWSABLE" />
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
140
141                <data
141-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:13-50
142                    android:host="firebase.auth"
143                    android:path="/"
144                    android:scheme="recaptcha" />
145            </intent-filter>
146        </activity>
147
148        <service
148-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
149            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
149-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
150            android:enabled="true"
150-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
151            android:exported="false" >
151-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
152            <meta-data
152-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
153                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
153-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
154                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
154-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
155        </service>
156
157        <activity
157-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
158            android:name="androidx.credentials.playservices.HiddenActivity"
158-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
159            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
159-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
160            android:enabled="true"
160-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
161            android:exported="false"
161-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
162            android:fitsSystemWindows="true"
162-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
163            android:theme="@style/Theme.Hidden" >
163-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
164        </activity>
165
166        <provider
166-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
167            android:name="com.google.firebase.provider.FirebaseInitProvider"
167-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
168            android:authorities="tn.dating.tunisian_dating_app.firebaseinitprovider"
168-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
169            android:directBootAware="true"
169-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
170            android:exported="false"
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
171            android:initOrder="100" />
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
172
173        <activity
173-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
174            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
174-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
175            android:excludeFromRecents="true"
175-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
176            android:exported="false"
176-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
177            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
177-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
178        <!--
179            Service handling Google Sign-In user revocation. For apps that do not integrate with
180            Google Sign-In, this service will never be started.
181        -->
182        <service
182-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
183            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
183-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
184            android:exported="true"
184-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
185            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
185-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
186            android:visibleToInstantApps="true" />
186-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
187
188        <activity
188-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
189            android:name="com.google.android.gms.common.api.GoogleApiActivity"
189-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
190            android:exported="false"
190-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
191            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
191-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
192
193        <uses-library
193-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
194            android:name="androidx.window.extensions"
194-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
195            android:required="false" />
195-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
196        <uses-library
196-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
197            android:name="androidx.window.sidecar"
197-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
198            android:required="false" />
198-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
199
200        <meta-data
200-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb4aceea626ef6ec2db1cf5b297099f2\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
201            android:name="com.google.android.gms.version"
201-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb4aceea626ef6ec2db1cf5b297099f2\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
202            android:value="@integer/google_play_services_version" />
202-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb4aceea626ef6ec2db1cf5b297099f2\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
203
204        <provider
204-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
205            android:name="androidx.startup.InitializationProvider"
205-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
206            android:authorities="tn.dating.tunisian_dating_app.androidx-startup"
206-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
207            android:exported="false" >
207-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
208            <meta-data
208-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
209                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
209-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
210                android:value="androidx.startup" />
210-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
211            <meta-data
211-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
212                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
212-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
213                android:value="androidx.startup" />
213-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
214        </provider>
215
216        <receiver
216-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
217            android:name="androidx.profileinstaller.ProfileInstallReceiver"
217-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
218            android:directBootAware="false"
218-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
219            android:enabled="true"
219-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
220            android:exported="true"
220-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
221            android:permission="android.permission.DUMP" >
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
222            <intent-filter>
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
223                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
224            </intent-filter>
225            <intent-filter>
225-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
226                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
226-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
227            </intent-filter>
228            <intent-filter>
228-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
229                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
229-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
230            </intent-filter>
231            <intent-filter>
231-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
232                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
233            </intent-filter>
234        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
235        <activity
235-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
236            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
236-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
237            android:exported="false"
237-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
238            android:stateNotNeeded="true"
238-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
239            android:theme="@style/Theme.PlayCore.Transparent" />
239-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
240    </application>
241
242</manifest>

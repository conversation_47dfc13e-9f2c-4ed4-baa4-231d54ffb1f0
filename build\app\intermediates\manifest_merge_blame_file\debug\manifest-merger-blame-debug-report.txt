1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="tn.dating.tunisian_dating_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:41:13-72
25-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:13-50
27-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29    </queries>
30
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:5-79
31-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:26:22-76
32    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\96d3ffafa270f328b6b1595e0942b698\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
32-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\96d3ffafa270f328b6b1595e0942b698\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="tn.dating.tunisian_dating_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="tn.dating.tunisian_dating_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\c666c258fca39b3353b46678a6b928ab\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:icon="@mipmap/ic_launcher"
46        android:label="tunisian_dating_app" >
47        <activity
48            android:name="tn.dating.tunisian_dating_app.MainActivity"
49            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
50            android:exported="true"
51            android:hardwareAccelerated="true"
52            android:launchMode="singleTop"
53            android:taskAffinity=""
54            android:theme="@style/LaunchTheme"
55            android:windowSoftInputMode="adjustResize" >
56
57            <!--
58                 Specifies an Android theme to apply to this Activity as soon as
59                 the Android process has started. This theme is visible to the user
60                 while the Flutter UI initializes. After that, this theme continues
61                 to determine the Window background behind the Flutter UI.
62            -->
63            <meta-data
64                android:name="io.flutter.embedding.android.NormalTheme"
65                android:resource="@style/NormalTheme" />
66
67            <intent-filter>
68                <action android:name="android.intent.action.MAIN" />
69
70                <category android:name="android.intent.category.LAUNCHER" />
71            </intent-filter>
72        </activity>
73        <!--
74             Don't delete the meta-data below.
75             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
76        -->
77        <meta-data
78            android:name="flutterEmbedding"
79            android:value="2" />
80
81        <service
81-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:9-12:19
82            android:name="com.google.firebase.components.ComponentDiscoveryService"
82-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:8:18-89
83            android:directBootAware="true"
83-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
84            android:exported="false" >
84-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:68:13-37
85            <meta-data
85-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
86                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
86-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-134
87                android:value="com.google.firebase.components.ComponentRegistrar" />
87-->[:cloud_firestore] C:\Users\<USER>\OneDrive\Desktop\dating app\build\cloud_firestore\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
88            <meta-data
88-->[:firebase_auth] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
89                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
89-->[:firebase_auth] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[:firebase_auth] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_auth\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
91            <meta-data
91-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:9:13-11:85
92                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
92-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:10:17-124
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[:firebase_core] C:\Users\<USER>\OneDrive\Desktop\dating app\build\firebase_core\intermediates\merged_manifest\debug\AndroidManifest.xml:11:17-82
94            <meta-data
94-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
95                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
95-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
97            <meta-data
97-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
98                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
98-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
99                android:value="com.google.firebase.components.ComponentRegistrar" />
99-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
100            <meta-data
100-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
101                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
101-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
102                android:value="com.google.firebase.components.ComponentRegistrar" />
102-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\transforms-3\3d29d95476e4e3fcd565abd93300fe28\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
103            <meta-data
103-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\de1f68a58916d382e87cebe6de5ae38f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
104                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
104-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\de1f68a58916d382e87cebe6de5ae38f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
105                android:value="com.google.firebase.components.ComponentRegistrar" />
105-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\de1f68a58916d382e87cebe6de5ae38f\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
106            <meta-data
106-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
107                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
107-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
108                android:value="com.google.firebase.components.ComponentRegistrar" />
108-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
109        </service>
110
111        <provider
111-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:9:9-17:20
112            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
112-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:10:13-82
113            android:authorities="tn.dating.tunisian_dating_app.flutter.image_provider"
113-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:11:13-74
114            android:exported="false"
114-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:12:13-37
115            android:grantUriPermissions="true" >
115-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:13:13-47
116            <meta-data
116-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:14:13-16:75
117                android:name="android.support.FILE_PROVIDER_PATHS"
117-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:15:17-67
118                android:resource="@xml/flutter_image_picker_file_paths" />
118-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:16:17-72
119        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
120        <service
120-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:19:9-31:19
121            android:name="com.google.android.gms.metadata.ModuleDependencies"
121-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:20:13-78
122            android:enabled="false"
122-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:21:13-36
123            android:exported="false" >
123-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:22:13-37
124            <intent-filter>
124-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:24:13-26:29
125                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
125-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:17-94
125-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:25:25-91
126            </intent-filter>
127
128            <meta-data
128-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:28:13-30:36
129                android:name="photopicker_activity:0:required"
129-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:29:17-63
130                android:value="" />
130-->[:image_picker_android] C:\Users\<USER>\OneDrive\Desktop\dating app\build\image_picker_android\intermediates\merged_manifest\debug\AndroidManifest.xml:30:17-33
131        </service>
132
133        <activity
133-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
134            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
134-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
135            android:excludeFromRecents="true"
135-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
136            android:exported="true"
136-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
137            android:launchMode="singleTask"
137-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
138            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
138-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
139            <intent-filter>
139-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
140                <action android:name="android.intent.action.VIEW" />
140-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
140-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
141
142                <category android:name="android.intent.category.DEFAULT" />
142-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
142-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
143                <category android:name="android.intent.category.BROWSABLE" />
143-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
143-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
144
145                <data
145-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:13-50
146                    android:host="firebase.auth"
147                    android:path="/"
148                    android:scheme="genericidp" />
149            </intent-filter>
150        </activity>
151        <activity
151-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
152            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
152-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
153            android:excludeFromRecents="true"
153-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
154            android:exported="true"
154-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
155            android:launchMode="singleTask"
155-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
156            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
156-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
157            <intent-filter>
157-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
158                <action android:name="android.intent.action.VIEW" />
158-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
158-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
159
160                <category android:name="android.intent.category.DEFAULT" />
160-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
160-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
161                <category android:name="android.intent.category.BROWSABLE" />
161-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
161-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\c88774b793b291c03076cbd33f2138e7\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
162
163                <data
163-->C:\Users\<USER>\OneDrive\Desktop\dating app\android\app\src\main\AndroidManifest.xml:42:13-50
164                    android:host="firebase.auth"
165                    android:path="/"
166                    android:scheme="recaptcha" />
167            </intent-filter>
168        </activity>
169
170        <provider
170-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
171            android:name="com.google.firebase.provider.FirebaseInitProvider"
171-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
172            android:authorities="tn.dating.tunisian_dating_app.firebaseinitprovider"
172-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
173            android:directBootAware="true"
173-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
174            android:exported="false"
174-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
175            android:initOrder="100" />
175-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\abd144767df15dffddd52de2fe329b95\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
176
177        <service
177-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
178            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
178-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
179            android:enabled="true"
179-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
180            android:exported="false" >
180-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
181            <meta-data
181-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
182                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
182-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
183                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
183-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
184        </service>
185
186        <activity
186-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
187            android:name="androidx.credentials.playservices.HiddenActivity"
187-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
188            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
188-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
189            android:enabled="true"
189-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
190            android:exported="false"
190-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
191            android:fitsSystemWindows="true"
191-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
192            android:theme="@style/Theme.Hidden" >
192-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\transforms-3\ee5298c99c04ce0d7253977776b1c013\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
193        </activity>
194        <activity
194-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
195            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
195-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
196            android:excludeFromRecents="true"
196-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
197            android:exported="false"
197-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
198            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
198-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
199        <!--
200            Service handling Google Sign-In user revocation. For apps that do not integrate with
201            Google Sign-In, this service will never be started.
202        -->
203        <service
203-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
204            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
204-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
205            android:exported="true"
205-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
206            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
206-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
207            android:visibleToInstantApps="true" />
207-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c05621c60381cb8fb926ec55335b5d39\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
208
209        <activity
209-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
210            android:name="com.google.android.gms.common.api.GoogleApiActivity"
210-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
211            android:exported="false"
211-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
212            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
212-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\8470c89ba64407e2dd527ce11792b982\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
213
214        <provider
214-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
215            android:name="androidx.startup.InitializationProvider"
215-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
216            android:authorities="tn.dating.tunisian_dating_app.androidx-startup"
216-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
217            android:exported="false" >
217-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
218            <meta-data
218-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
219                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
219-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
220                android:value="androidx.startup" />
220-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d9083683aa46ad6646ca7a884bd56df\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
221            <meta-data
221-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
222                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
222-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
223                android:value="androidx.startup" />
223-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
224        </provider>
225
226        <uses-library
226-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
227            android:name="androidx.window.extensions"
227-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
228            android:required="false" />
228-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
229        <uses-library
229-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
230            android:name="androidx.window.sidecar"
230-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
231            android:required="false" />
231-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\3e108fe9d597d03770ae82acaebb5b63\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
232
233        <meta-data
233-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb4aceea626ef6ec2db1cf5b297099f2\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
234            android:name="com.google.android.gms.version"
234-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb4aceea626ef6ec2db1cf5b297099f2\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
235            android:value="@integer/google_play_services_version" />
235-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\bb4aceea626ef6ec2db1cf5b297099f2\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
236
237        <receiver
237-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
238            android:name="androidx.profileinstaller.ProfileInstallReceiver"
238-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
239            android:directBootAware="false"
239-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
240            android:enabled="true"
240-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
241            android:exported="true"
241-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
242            android:permission="android.permission.DUMP" >
242-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
243            <intent-filter>
243-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
244                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
244-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
245            </intent-filter>
246            <intent-filter>
246-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
247                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
247-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
248            </intent-filter>
249            <intent-filter>
249-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
250                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
251            </intent-filter>
252            <intent-filter>
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
253                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1e1e8719823df11c58e964840aa823f6\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
254            </intent-filter>
255        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
256        <activity
256-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
257            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
257-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
258            android:exported="false"
258-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
259            android:stateNotNeeded="true"
259-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
260            android:theme="@style/Theme.PlayCore.Transparent" />
260-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\transforms-3\1cca11a658df86599d5d661195fa5754\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
261    </application>
262
263</manifest>

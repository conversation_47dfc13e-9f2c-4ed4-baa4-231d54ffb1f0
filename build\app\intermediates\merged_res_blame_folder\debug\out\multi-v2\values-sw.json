{"logs": [{"outputFile": "tn.dating.tunisian_dating_app-mergeDebugResources-47:/values-sw/values-sw.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,175,272,349,499,668,753", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "170,267,344,494,663,748,831"}, "to": {"startLines": "56,58,62,63,66,67,68", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6019,6203,6621,6698,7031,7200,7285", "endColumns": "69,96,76,149,168,84,82", "endOffsets": "6084,6295,6693,6843,7195,7280,7363"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb4aceea626ef6ec2db1cf5b297099f2\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sw\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "141", "endOffsets": "336"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4773", "endColumns": "145", "endOffsets": "4914"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5ecd5a1cc2195013671502cd2470e636\\transformed\\browser-1.4.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,169,270,387", "endColumns": "113,100,116,102", "endOffsets": "164,265,382,485"}, "to": {"startLines": "57,59,60,61", "startColumns": "4,4,4,4", "startOffsets": "6089,6300,6401,6518", "endColumns": "113,100,116,102", "endOffsets": "6198,6396,6513,6616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\24c1e520e2b1e7ad04d8500a33fb9ad9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,121", "endOffsets": "162,284"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2801,2913", "endColumns": "111,121", "endOffsets": "2908,3030"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\6dadd0bfa2d44ddeab91e958645034df\\transformed\\jetified-play-services-base-18.0.1\\res\\values-sw\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,445,566,671,830,951,1066,1176,1337,1439,1589,1712,1858,2013,2077,2148", "endColumns": "99,151,120,104,158,120,114,109,160,101,149,122,145,154,63,70,91", "endOffsets": "292,444,565,670,829,950,1065,1175,1336,1438,1588,1711,1857,2012,2076,2147,2239"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3758,3862,4018,4143,4252,4415,4540,4659,4919,5084,5190,5344,5471,5621,5780,5848,5923", "endColumns": "103,155,124,108,162,124,118,113,164,105,153,126,149,158,67,74,95", "endOffsets": "3857,4013,4138,4247,4410,4535,4654,4768,5079,5185,5339,5466,5616,5775,5843,5918,6014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3a04377ab98aaf9189ccddcb951bae89\\transformed\\appcompat-1.1.0\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1911,2012,2120,2219,2326,2438,2542,2704,2801", "endColumns": "102,98,107,89,104,116,82,82,90,91,94,93,100,92,94,93,90,90,80,100,107,98,106,111,103,161,96,81", "endOffsets": "203,302,410,500,605,722,805,888,979,1071,1166,1260,1361,1454,1549,1643,1734,1825,1906,2007,2115,2214,2321,2433,2537,2699,2796,2878"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,307,415,505,610,727,810,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1911,2012,2120,2219,2326,2438,2542,2704,6848", "endColumns": "102,98,107,89,104,116,82,82,90,91,94,93,100,92,94,93,90,90,80,100,107,98,106,111,103,161,96,81", "endOffsets": "203,302,410,500,605,722,805,888,979,1071,1166,1260,1361,1454,1549,1643,1734,1825,1906,2007,2115,2214,2321,2433,2537,2699,2796,6925"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values-sw\\values-sw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,449,556,663,778", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "144,246,343,444,551,658,773,874"}, "to": {"startLines": "31,32,33,34,35,36,37,65", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3035,3129,3231,3328,3429,3536,3643,6930", "endColumns": "93,101,96,100,106,106,114,100", "endOffsets": "3124,3226,3323,3424,3531,3638,3753,7026"}}]}]}
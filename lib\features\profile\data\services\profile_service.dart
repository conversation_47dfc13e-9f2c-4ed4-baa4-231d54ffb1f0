import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../models/user_profile.dart';

class ProfileService {
  static final ProfileService _instance = ProfileService._internal();
  factory ProfileService() => _instance;
  ProfileService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // Collection references
  CollectionReference get _usersCollection => _firestore.collection('users');

  /// Create a new user profile after phone verification
  Future<void> createUserProfile({
    required String phoneNumber,
  }) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final now = DateTime.now();
    final defaultPreferences = UserPreferences();
    final defaultPrivacy = PrivacySettings();

    final userProfile = UserProfile(
      userId: user.uid,
      phoneNumber: phoneNumber,
      preferences: defaultPreferences,
      privacy: defaultPrivacy,
      createdAt: now,
      updatedAt: now,
    );

    await _usersCollection.doc(user.uid).set(userProfile.toMap());
  }

  /// Update basic information
  Future<void> updateBasicInfo({
    required String firstName,
    required String lastName,
    required DateTime dateOfBirth,
    String? bio,
    String? city,
    String? governorate,
  }) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    await _usersCollection.doc(user.uid).update({
      'firstName': firstName,
      'lastName': lastName,
      'dateOfBirth': dateOfBirth.toIso8601String(),
      'bio': bio,
      'city': city,
      'governorate': governorate,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  /// Upload photos to Firebase Storage and update profile
  Future<List<String>> uploadPhotos(List<File> photos) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    List<String> photoUrls = [];

    for (int i = 0; i < photos.length; i++) {
      final file = photos[i];
      final fileName = 'users/${user.uid}/photos/photo_$i.jpg';
      
      try {
        // Upload to Firebase Storage
        final ref = _storage.ref().child(fileName);
        final uploadTask = await ref.putFile(file);
        final downloadUrl = await uploadTask.ref.getDownloadURL();
        photoUrls.add(downloadUrl);
      } catch (e) {
        throw Exception('Failed to upload photo ${i + 1}: $e');
      }
    }

    // Update profile with photo URLs
    await _usersCollection.doc(user.uid).update({
      'photos': photoUrls,
      'profilePhotoUrl': photoUrls.isNotEmpty ? photoUrls.first : null,
      'updatedAt': DateTime.now().toIso8601String(),
    });

    return photoUrls;
  }

  /// Update interests and preferences
  Future<void> updateInterestsAndPreferences({
    required List<String> interests,
    required List<String> interestedIn,
    required int minAge,
    required int maxAge,
    required int maxDistance,
    required bool showOnlyVerified,
    required bool showDistance,
  }) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final preferences = UserPreferences(
      minAge: minAge,
      maxAge: maxAge,
      maxDistance: maxDistance,
      interestedIn: interestedIn,
      showOnlyVerified: showOnlyVerified,
      showDistance: showDistance,
    );

    await _usersCollection.doc(user.uid).update({
      'interests': interests,
      'preferences': preferences.toMap(),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  /// Update privacy settings
  Future<void> updatePrivacySettings({
    required bool blurPhotos,
    required bool hideAge,
    required bool hideDistance,
    required bool hideLastSeen,
    required bool onlyMatchedCanMessage,
    required bool showOnlineStatus,
  }) async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    final privacy = PrivacySettings(
      blurPhotos: blurPhotos,
      hideAge: hideAge,
      hideDistance: hideDistance,
      hideLastSeen: hideLastSeen,
      onlyMatchedCanMessage: onlyMatchedCanMessage,
      showOnlineStatus: showOnlineStatus,
    );

    await _usersCollection.doc(user.uid).update({
      'privacy': privacy.toMap(),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  /// Complete profile setup
  Future<void> completeProfileSetup() async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    await _usersCollection.doc(user.uid).update({
      'isProfileComplete': true,
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  /// Get current user profile
  Future<UserProfile?> getCurrentUserProfile() async {
    final user = _auth.currentUser;
    if (user == null) return null;

    try {
      final doc = await _usersCollection.doc(user.uid).get();
      if (doc.exists) {
        return UserProfile.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user profile: $e');
    }
  }

  /// Stream current user profile
  Stream<UserProfile?> getCurrentUserProfileStream() {
    final user = _auth.currentUser;
    if (user == null) return Stream.value(null);

    return _usersCollection.doc(user.uid).snapshots().map((doc) {
      if (doc.exists) {
        return UserProfile.fromFirestore(doc);
      }
      return null;
    });
  }

  /// Get user profile by ID
  Future<UserProfile?> getUserProfile(String userId) async {
    try {
      final doc = await _usersCollection.doc(userId).get();
      if (doc.exists) {
        return UserProfile.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user profile: $e');
    }
  }

  /// Search for potential matches
  Future<List<UserProfile>> searchPotentialMatches({
    required UserPreferences preferences,
    required String currentUserId,
    int limit = 20,
  }) async {
    try {
      Query query = _usersCollection
          .where('isProfileComplete', isEqualTo: true)
          .where('userId', isNotEqualTo: currentUserId);

      // Filter by age if preferences specify
      if (preferences.minAge > 0 || preferences.maxAge < 100) {
        // Note: This is a simplified age filter
        // In production, you'd want to use a more sophisticated approach
        // possibly with cloud functions for complex queries
      }

      // Filter by verification status if required
      if (preferences.showOnlyVerified) {
        query = query.where('isVerified', isEqualTo: true);
      }

      final querySnapshot = await query.limit(limit).get();
      
      return querySnapshot.docs
          .map((doc) => UserProfile.fromFirestore(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to search matches: $e');
    }
  }

  /// Update user's last seen timestamp
  Future<void> updateLastSeen() async {
    final user = _auth.currentUser;
    if (user == null) return;

    await _usersCollection.doc(user.uid).update({
      'lastSeen': FieldValue.serverTimestamp(),
    });
  }

  /// Delete user profile and all associated data
  Future<void> deleteUserProfile() async {
    final user = _auth.currentUser;
    if (user == null) throw Exception('User not authenticated');

    // Delete photos from storage
    try {
      final listResult = await _storage.ref('users/${user.uid}/photos').listAll();
      for (final item in listResult.items) {
        await item.delete();
      }
    } catch (e) {
      // Photos might not exist, continue with profile deletion
    }

    // Delete profile document
    await _usersCollection.doc(user.uid).delete();
  }

  /// Check if user has completed profile setup
  Future<bool> hasCompletedProfile() async {
    final user = _auth.currentUser;
    if (user == null) return false;

    try {
      final doc = await _usersCollection.doc(user.uid).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return data['isProfileComplete'] ?? false;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}

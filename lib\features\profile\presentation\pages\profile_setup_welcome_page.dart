import 'package:flutter/material.dart';
import 'basic_info_page.dart';

class ProfileSetupWelcomePage extends StatelessWidget {
  const ProfileSetupWelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: <PERSON><PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: <PERSON>um<PERSON>(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),

              // Header
              const Icon(
                Icons.favorite,
                size: 80,
                color: Color(0xFFE74C3C),
              ),
              const SizedBox(height: 24),

              Text(
                'Let\'s Create Your Profile',
                style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 12),

              Text(
                'Help us create the perfect profile to find your ideal match in Tunisia',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: Colors.grey[600],
                      height: 1.5,
                    ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 48),

              // Profile setup steps
              _buildSetupStep(
                context,
                icon: Icons.person,
                title: 'Basic Information',
                description: 'Name, age, and location',
                stepNumber: 1,
              ),

              const SizedBox(height: 20),

              _buildSetupStep(
                context,
                icon: Icons.camera_alt,
                title: 'Add Photos',
                description: 'Show your best self',
                stepNumber: 2,
              ),

              const SizedBox(height: 20),

              _buildSetupStep(
                context,
                icon: Icons.favorite_border,
                title: 'Interests & Preferences',
                description: 'What you love and what you\'re looking for',
                stepNumber: 3,
              ),

              const SizedBox(height: 20),

              _buildSetupStep(
                context,
                icon: Icons.security,
                title: 'Privacy Settings',
                description: 'Control who sees your profile',
                stepNumber: 4,
              ),

              const Spacer(),

              // Cultural note
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFE74C3C).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFE74C3C).withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.info_outline,
                      color: Color(0xFFE74C3C),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'We respect Tunisian culture and values. Your privacy and safety are our top priorities.',
                        style: TextStyle(
                          color: const Color(0xFFE74C3C).withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Start button
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const BasicInfoPage(),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'Let\'s Get Started',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Skip for now button
              TextButton(
                onPressed: () {
                  // TODO: Navigate to main app with incomplete profile
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          'You can complete your profile later in settings'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                },
                child: Text(
                  'Skip for now',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSetupStep(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required int stepNumber,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Step number
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: const Color(0xFFE74C3C).withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: Text(
                stepNumber.toString(),
                style: const TextStyle(
                  color: Color(0xFFE74C3C),
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Icon
          Icon(
            icon,
            color: const Color(0xFFE74C3C),
            size: 24,
          ),

          const SizedBox(width: 16),

          // Text content
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Arrow
          Icon(
            Icons.arrow_forward_ios,
            color: Colors.grey[400],
            size: 16,
          ),
        ],
      ),
    );
  }
}

name: tunisian_dating_app
description: "A Tunisian dating app - private, local, and culturally respectful."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.3

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI & Navigation
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.1
  photo_view: ^0.15.0
  flutter_staggered_grid_view: ^0.7.0

  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1

  # Firebase & Backend
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3

  # Authentication & Phone Verification
  phone_numbers_parser: ^8.3.0
  country_code_picker: ^3.0.0
  pin_code_fields: ^8.0.1

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # Image & Media
  image_picker: ^1.1.2
  image_cropper: ^8.0.2
  permission_handler: ^11.3.1

  # Location & Maps
  geolocator: ^13.0.1
  geocoding: ^3.0.0

  # Chat & Communication
  flutter_chat_ui: ^1.6.15
  record: ^5.1.2
  audioplayers: ^6.1.0

  # Animations & UI Effects
  lottie: ^3.1.2
  shimmer: ^3.0.0
  flutter_animate: ^4.5.0

  # Utilities
  shared_preferences: ^2.3.2
  connectivity_plus: ^6.0.5
  url_launcher: ^6.3.1
  package_info_plus: ^8.0.2
  device_info_plus: ^10.1.2

  # HTTP & API
  http: ^1.2.2
  dio: ^5.7.0

  # Date & Time
  timeago: ^3.7.0

  # Swipe Cards
  flutter_card_swiper: ^7.0.1

  # Payment (for premium features)
  in_app_purchase: ^3.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for the Tunisian dating app
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/flags/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors - Inspired by Tunisian culture
  static const Color primary = Color(0xFFE74C3C); // Warm red - passion and love
  static const Color primaryLight = Color(0xFFFF6B6B);
  static const Color primaryDark = Color(0xFFC0392B);
  
  // Secondary Colors - Mediterranean vibes
  static const Color secondary = Color(0xFF3498DB); // Mediterranean blue
  static const Color secondaryLight = Color(0xFF5DADE2);
  static const Color secondaryDark = Color(0xFF2980B9);
  
  // Accent Colors
  static const Color accent = Color(0xFFF39C12); // Warm orange - Tunisian sunset
  static const Color accentLight = Color(0xFFFFB74D);
  static const Color accentDark = Color(0xFFE67E22);
  
  // Neutral Colors
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color grey = Color(0xFF95A5A6);
  static const Color greyLight = Color(0xFFECF0F1);
  static const Color greyDark = Color(0xFF7F8C8D);
  
  // Background Colors
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF2C3E50);
  static const Color textSecondary = Color(0xFF7F8C8D);
  static const Color textLight = Color(0xFFBDC3C7);
  static const Color textOnPrimary = Color(0xFFFFFFFF);
  
  // Status Colors
  static const Color success = Color(0xFF27AE60);
  static const Color warning = Color(0xFFF39C12);
  static const Color error = Color(0xFFE74C3C);
  static const Color info = Color(0xFF3498DB);
  
  // Special Colors
  static const Color online = Color(0xFF2ECC71);
  static const Color offline = Color(0xFF95A5A6);
  static const Color verified = Color(0xFF3498DB);
  static const Color premium = Color(0xFFFFD700);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryLight, primary],
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [secondaryLight, secondary],
  );
  
  static const LinearGradient accentGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [accentLight, accent],
  );
  
  // Card Colors
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color cardShadow = Color(0x1A000000);
  
  // Button Colors
  static const Color buttonPrimary = primary;
  static const Color buttonSecondary = secondary;
  static const Color buttonDisabled = Color(0xFFBDC3C7);
  
  // Input Colors
  static const Color inputBorder = Color(0xFFE0E0E0);
  static const Color inputFocused = primary;
  static const Color inputError = error;
  static const Color inputBackground = Color(0xFFFAFAFA);
  
  // Chat Colors
  static const Color chatBubbleSent = primary;
  static const Color chatBubbleReceived = Color(0xFFF0F0F0);
  static const Color chatTextSent = white;
  static const Color chatTextReceived = textPrimary;
  
  // Match Colors
  static const Color likeColor = Color(0xFF2ECC71);
  static const Color passColor = Color(0xFFE74C3C);
  static const Color superLikeColor = Color(0xFF3498DB);
  
  // Privacy Colors
  static const Color blurOverlay = Color(0x80000000);
  static const Color privateMode = Color(0xFF8E44AD);
  
  // Cultural Colors (inspired by Tunisian flag and culture)
  static const Color tunisianRed = Color(0xFFE70013);
  static const Color tunisianWhite = Color(0xFFFFFFFF);
  static const Color crescentGreen = Color(0xFF00A651);
  
  // Dark Theme Colors
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkPrimary = Color(0xFFFF6B6B);
  static const Color darkTextPrimary = Color(0xFFFFFFFF);
  static const Color darkTextSecondary = Color(0xFFB0B0B0);
  
  // Shimmer Colors
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
  
  // Border Colors
  static const Color borderLight = Color(0xFFE0E0E0);
  static const Color borderMedium = Color(0xFFBDBDBD);
  static const Color borderDark = Color(0xFF757575);
  
  // Overlay Colors
  static const Color overlayLight = Color(0x33000000);
  static const Color overlayMedium = Color(0x66000000);
  static const Color overlayDark = Color(0x99000000);
  
  // Helper method to get color with opacity
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }
  
  // Helper method to get contrasting text color
  static Color getContrastingTextColor(Color backgroundColor) {
    final luminance = backgroundColor.computeLuminance();
    return luminance > 0.5 ? textPrimary : white;
  }
}

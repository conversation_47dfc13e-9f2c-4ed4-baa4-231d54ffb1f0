{"logs": [{"outputFile": "tn.dating.tunisian_dating_app-mergeDebugResources-49:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c666c258fca39b3353b46678a6b928ab\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "29,70,71,88,89,120,121,250,251,252,253,254,255,256,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,369,370,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,416,448,449,450,451,452,453,454,490,2035,2036,2040,2041,2045,2201,2202,2909,2926,3096,3129,3159,3192", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "985,2715,2787,4084,4149,6239,6308,15190,15260,15328,15400,15470,15531,15605,16848,16909,16970,17032,17096,17158,17219,17287,17387,17447,17513,17586,17655,17712,17764,20144,20216,20292,20357,20416,20475,20535,20595,20655,20715,20775,20835,20895,20955,21015,21075,21134,21194,21254,21314,21374,21434,21494,21554,21614,21674,21734,21793,21853,21913,21972,22031,22090,22149,22208,22776,22811,23397,23452,23515,23570,23628,23686,23747,23810,23867,23918,23968,24029,24086,24152,24186,24221,25262,27515,27582,27654,27723,27792,27866,27938,31572,132385,132502,132703,132813,133014,145071,145143,169566,170170,178005,179736,180736,181418", "endLines": "29,70,71,88,89,120,121,250,251,252,253,254,255,256,272,273,274,275,276,277,278,279,280,281,282,283,284,285,286,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,340,341,342,343,344,345,346,347,348,349,350,351,352,353,354,355,356,357,358,369,370,382,383,384,385,386,387,388,389,390,391,392,393,394,395,396,397,416,448,449,450,451,452,453,454,490,2035,2039,2040,2044,2045,2201,2202,2914,2935,3128,3149,3191,3197", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "1040,2782,2870,4144,4210,6303,6366,15255,15323,15395,15465,15526,15600,15673,16904,16965,17027,17091,17153,17214,17282,17382,17442,17508,17581,17650,17707,17759,17821,20211,20287,20352,20411,20470,20530,20590,20650,20710,20770,20830,20890,20950,21010,21070,21129,21189,21249,21309,21369,21429,21489,21549,21609,21669,21729,21788,21848,21908,21967,22026,22085,22144,22203,22262,22806,22841,23447,23510,23565,23623,23681,23742,23805,23862,23913,23963,24024,24081,24147,24181,24216,24251,25327,27577,27649,27718,27787,27861,27933,28021,31638,132497,132698,132808,133009,133138,145138,145205,169764,170466,179731,180412,181413,181580"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\dating app\\build\\app\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7", "startColumns": "4,4,4,4,4,4", "startOffsets": "55,137,241,350,470,577", "endColumns": "81,103,108,119,106,75", "endOffsets": "132,236,345,465,572,648"}, "to": {"startLines": "479,480,481,482,483,488", "startColumns": "4,4,4,4,4,4", "startOffsets": "30737,30819,30923,31032,31152,31443", "endColumns": "81,103,108,119,106,75", "endOffsets": "30814,30918,31027,31147,31254,31514"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\1cca11a658df86599d5d661195fa5754\\transformed\\jetified-core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "2086", "startColumns": "4", "startOffsets": "136002", "endLines": "2093", "endColumns": "8", "endOffsets": "136407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8c1255052c21d15b5cf9e9f37c5c01b9\\transformed\\jetified-appcompat-resources-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2378,2394,2400,3724,3740", "startColumns": "4,4,4,4,4", "startOffsets": "152438,152863,153041,198617,199028", "endLines": "2393,2399,2409,3739,3743", "endColumns": "24,24,24,24,24", "endOffsets": "152858,153036,153320,199023,199150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c8996eb6ce8efabee1883fe1486006c9\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "404", "startColumns": "4", "startOffsets": "24554", "endColumns": "42", "endOffsets": "24592"}}, {"source": "C:\\Users\\<USER>\\OneDrive\\Desktop\\dating app\\android\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "3,14", "startColumns": "4,4", "startOffsets": "176,832", "endLines": "7,16", "endColumns": "12,12", "endOffsets": "483,998"}, "to": {"startLines": "1606,1610", "startColumns": "4,4", "startOffsets": "103293,103474", "endLines": "1609,1612", "endColumns": "12,12", "endOffsets": "103469,103638"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ee5298c99c04ce0d7253977776b1c013\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "2082", "startColumns": "4", "startOffsets": "135779", "endLines": "2085", "endColumns": "12", "endOffsets": "135997"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\858939ab556fe096523710f298b68127\\transformed\\appcompat-1.3.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,221,222,226,230,234,239,245,252,256,260,265,269,273,277,281,285,289,295,299,305,309,315,319,324,328,331,335,341,345,351,355,361,364,368,372,376,380,384,385,386,387,390,393,396,399,403,404,405,406,407,410,412,414,416,421,422,426,432,436,437,439,451,452,456,462,466,467,468,472,499,503,504,508,536,708,734,906,932,963,971,977,993,1015,1020,1025,1035,1044,1053,1057,1064,1083,1090,1091,1100,1103,1106,1110,1114,1118,1121,1122,1127,1132,1142,1147,1154,1160,1161,1164,1168,1173,1175,1177,1180,1183,1185,1189,1192,1199,1202,1205,1209,1211,1215,1217,1219,1221,1225,1233,1241,1253,1259,1268,1271,1282,1285,1286,1291,1292,1297,1366,1436,1437,1447,1456,1457,1459,1463,1466,1469,1472,1475,1478,1481,1484,1488,1491,1494,1497,1501,1504,1508,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1532,1534,1536,1537,1538,1539,1540,1541,1542,1543,1545,1546,1548,1549,1551,1553,1554,1556,1557,1558,1559,1560,1561,1563,1564,1565,1566,1567,1568,1570,1572,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1586,1588,1589,1590,1591,1592,1593,1594,1596,1600,1604,1605,1606,1607,1608,1609,1613,1614,1615,1616,1618,1620,1622,1624,1626,1627,1628,1629,1631,1633,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1646,1649,1650,1651,1652,1654,1656,1657,1659,1660,1662,1664,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1677,1679,1680,1681,1682,1684,1685,1686,1687,1688,1690,1692,1694,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1794,1797,1800,1803,1817,1828,1838,1868,1895,1904,1979,2382,2387,2415,2433,2469,2475,2481,2504,2645,2665,2671,2675,2681,2718,2730,2796,2820,2889,2908,2934", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,412,476,546,607,682,758,835,913,998,1080,1156,1232,1309,1387,1493,1599,1678,1758,1815,1873,1947,2022,2087,2153,2213,2274,2346,2419,2486,2554,2613,2672,2731,2790,2849,2903,2957,3010,3064,3118,3172,3226,3300,3379,3452,3526,3597,3669,3741,3814,3871,3929,4002,4076,4150,4225,4297,4370,4440,4511,4571,4632,4701,4770,4840,4914,4990,5054,5131,5207,5284,5349,5418,5495,5570,5639,5707,5784,5850,5911,6008,6073,6142,6241,6312,6371,6429,6486,6545,6609,6680,6752,6824,6896,6968,7035,7103,7171,7230,7293,7357,7447,7538,7598,7664,7731,7797,7867,7931,7984,8051,8112,8179,8292,8350,8413,8478,8543,8618,8691,8763,8807,8854,8900,8949,9010,9071,9132,9194,9258,9322,9386,9451,9514,9574,9635,9701,9760,9820,9882,9953,10013,10081,10167,10254,10344,10431,10519,10601,10684,10774,10865,10917,10975,11020,11086,11150,11207,11264,11318,11375,11423,11472,11523,11557,11604,11653,11699,11731,11795,11857,11917,11974,12048,12118,12196,12250,12320,12405,12453,12499,12560,12623,12689,12753,12824,12887,12952,13016,13077,13138,13190,13263,13337,13406,13481,13555,13629,13770,13840,13893,13971,14061,14149,14245,14335,14917,15006,15253,15534,15786,16071,16464,16941,17163,17385,17661,17888,18118,18348,18578,18808,19035,19454,19680,20105,20335,20763,20982,21265,21473,21604,21831,22257,22482,22909,23130,23555,23675,23951,24252,24576,24867,25181,25318,25449,25554,25796,25963,26167,26375,26646,26758,26870,26975,27092,27306,27452,27592,27678,28026,28114,28360,28778,29027,29109,29207,29889,29989,30241,30665,30920,31014,31103,31340,33392,33634,33736,33989,36173,47362,48878,60165,61693,63450,64076,64496,65757,67022,67278,67514,68061,68555,69160,69358,69938,71306,71681,71799,72337,72494,72690,72963,73219,73389,73530,73594,73959,74326,75002,75266,75604,75957,76051,76237,76543,76805,76930,77057,77296,77507,77626,77819,77996,78451,78632,78754,79013,79126,79313,79415,79522,79651,79926,80434,80930,81807,82101,82671,82820,83552,83724,83808,84144,84236,84514,89908,95442,95504,96134,96748,96839,96952,97181,97341,97493,97664,97830,97999,98166,98329,98572,98742,98915,99086,99360,99559,99764,100094,100178,100274,100370,100468,100568,100670,100772,100874,100976,101078,101178,101274,101386,101515,101638,101769,101900,101998,102112,102206,102346,102480,102576,102688,102788,102904,103000,103112,103212,103352,103488,103652,103782,103940,104090,104231,104375,104510,104622,104772,104900,105028,105164,105296,105426,105556,105668,105808,105954,106098,106236,106302,106392,106468,106572,106662,106764,106872,106980,107080,107160,107252,107350,107460,107512,107590,107696,107788,107892,108002,108124,108287,108444,108524,108624,108714,108824,108914,109155,109249,109355,109447,109547,109659,109773,109889,110005,110099,110213,110325,110427,110547,110669,110751,110855,110975,111101,111199,111293,111381,111493,111609,111731,111843,112018,112134,112220,112312,112424,112548,112615,112741,112809,112937,113081,113209,113278,113373,113488,113601,113700,113809,113920,114031,114132,114237,114337,114467,114558,114681,114775,114887,114973,115077,115173,115261,115379,115483,115587,115713,115801,115909,116009,116099,116209,116293,116395,116479,116533,116597,116703,116789,116899,116983,117103,122247,122365,122480,122612,123327,124019,124536,126135,127668,128056,132791,153454,153714,155224,156257,158270,158532,158888,159718,166500,167634,167928,168151,168478,170528,171176,175027,176229,180308,181523,182932", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,220,221,225,229,233,238,244,251,255,259,264,268,272,276,280,284,288,294,298,304,308,314,318,323,327,330,334,340,344,350,354,360,363,367,371,375,379,383,384,385,386,389,392,395,398,402,403,404,405,406,409,411,413,415,420,421,425,431,435,436,438,450,451,455,461,465,466,467,471,498,502,503,507,535,707,733,905,931,962,970,976,992,1014,1019,1024,1034,1043,1052,1056,1063,1082,1089,1090,1099,1102,1105,1109,1113,1117,1120,1121,1126,1131,1141,1146,1153,1159,1160,1163,1167,1172,1174,1176,1179,1182,1184,1188,1191,1198,1201,1204,1208,1210,1214,1216,1218,1220,1224,1232,1240,1252,1258,1267,1270,1281,1284,1285,1290,1291,1296,1365,1435,1436,1446,1455,1456,1458,1462,1465,1468,1471,1474,1477,1480,1483,1487,1490,1493,1496,1500,1503,1507,1511,1512,1513,1514,1515,1516,1517,1518,1519,1520,1521,1522,1523,1524,1525,1526,1527,1528,1529,1530,1531,1533,1535,1536,1537,1538,1539,1540,1541,1542,1544,1545,1547,1548,1550,1552,1553,1555,1556,1557,1558,1559,1560,1562,1563,1564,1565,1566,1567,1569,1571,1573,1574,1575,1576,1577,1578,1579,1580,1581,1582,1583,1584,1585,1587,1588,1589,1590,1591,1592,1593,1595,1599,1603,1604,1605,1606,1607,1608,1612,1613,1614,1615,1617,1619,1621,1623,1625,1626,1627,1628,1630,1632,1634,1635,1636,1637,1638,1639,1640,1641,1642,1643,1644,1645,1648,1649,1650,1651,1653,1655,1656,1658,1659,1661,1663,1665,1666,1667,1668,1669,1670,1671,1672,1673,1674,1675,1676,1678,1679,1680,1681,1683,1684,1685,1686,1687,1689,1691,1693,1695,1696,1697,1698,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1793,1796,1799,1802,1816,1827,1837,1867,1894,1903,1978,2381,2386,2414,2432,2468,2474,2480,2503,2644,2664,2670,2674,2680,2717,2729,2795,2819,2888,2907,2933,2942", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,407,471,541,602,677,753,830,908,993,1075,1151,1227,1304,1382,1488,1594,1673,1753,1810,1868,1942,2017,2082,2148,2208,2269,2341,2414,2481,2549,2608,2667,2726,2785,2844,2898,2952,3005,3059,3113,3167,3221,3295,3374,3447,3521,3592,3664,3736,3809,3866,3924,3997,4071,4145,4220,4292,4365,4435,4506,4566,4627,4696,4765,4835,4909,4985,5049,5126,5202,5279,5344,5413,5490,5565,5634,5702,5779,5845,5906,6003,6068,6137,6236,6307,6366,6424,6481,6540,6604,6675,6747,6819,6891,6963,7030,7098,7166,7225,7288,7352,7442,7533,7593,7659,7726,7792,7862,7926,7979,8046,8107,8174,8287,8345,8408,8473,8538,8613,8686,8758,8802,8849,8895,8944,9005,9066,9127,9189,9253,9317,9381,9446,9509,9569,9630,9696,9755,9815,9877,9948,10008,10076,10162,10249,10339,10426,10514,10596,10679,10769,10860,10912,10970,11015,11081,11145,11202,11259,11313,11370,11418,11467,11518,11552,11599,11648,11694,11726,11790,11852,11912,11969,12043,12113,12191,12245,12315,12400,12448,12494,12555,12618,12684,12748,12819,12882,12947,13011,13072,13133,13185,13258,13332,13401,13476,13550,13624,13765,13835,13888,13966,14056,14144,14240,14330,14912,15001,15248,15529,15781,16066,16459,16936,17158,17380,17656,17883,18113,18343,18573,18803,19030,19449,19675,20100,20330,20758,20977,21260,21468,21599,21826,22252,22477,22904,23125,23550,23670,23946,24247,24571,24862,25176,25313,25444,25549,25791,25958,26162,26370,26641,26753,26865,26970,27087,27301,27447,27587,27673,28021,28109,28355,28773,29022,29104,29202,29884,29984,30236,30660,30915,31009,31098,31335,33387,33629,33731,33984,36168,47357,48873,60160,61688,63445,64071,64491,65752,67017,67273,67509,68056,68550,69155,69353,69933,71301,71676,71794,72332,72489,72685,72958,73214,73384,73525,73589,73954,74321,74997,75261,75599,75952,76046,76232,76538,76800,76925,77052,77291,77502,77621,77814,77991,78446,78627,78749,79008,79121,79308,79410,79517,79646,79921,80429,80925,81802,82096,82666,82815,83547,83719,83803,84139,84231,84509,89903,95437,95499,96129,96743,96834,96947,97176,97336,97488,97659,97825,97994,98161,98324,98567,98737,98910,99081,99355,99554,99759,100089,100173,100269,100365,100463,100563,100665,100767,100869,100971,101073,101173,101269,101381,101510,101633,101764,101895,101993,102107,102201,102341,102475,102571,102683,102783,102899,102995,103107,103207,103347,103483,103647,103777,103935,104085,104226,104370,104505,104617,104767,104895,105023,105159,105291,105421,105551,105663,105803,105949,106093,106231,106297,106387,106463,106567,106657,106759,106867,106975,107075,107155,107247,107345,107455,107507,107585,107691,107783,107887,107997,108119,108282,108439,108519,108619,108709,108819,108909,109150,109244,109350,109442,109542,109654,109768,109884,110000,110094,110208,110320,110422,110542,110664,110746,110850,110970,111096,111194,111288,111376,111488,111604,111726,111838,112013,112129,112215,112307,112419,112543,112610,112736,112804,112932,113076,113204,113273,113368,113483,113596,113695,113804,113915,114026,114127,114232,114332,114462,114553,114676,114770,114882,114968,115072,115168,115256,115374,115478,115582,115708,115796,115904,116004,116094,116204,116288,116390,116474,116528,116592,116698,116784,116894,116978,117098,122242,122360,122475,122607,123322,124014,124531,126130,127663,128051,132786,153449,153709,155219,156252,158265,158527,158883,159713,166495,167629,167923,168146,168473,170523,171171,175022,176224,180303,181518,182927,183401"}, "to": {"startLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,257,258,262,263,264,265,266,267,268,294,295,296,297,298,299,300,301,359,360,361,362,367,375,376,381,403,409,410,411,412,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,489,502,503,504,505,506,507,515,516,520,524,528,533,539,546,550,554,559,563,567,571,575,579,583,589,593,599,603,609,613,618,622,625,629,635,639,645,649,655,658,662,666,670,674,678,679,680,681,684,687,690,693,697,698,699,700,701,704,706,708,710,715,716,720,726,730,731,733,745,746,750,756,760,761,762,766,793,797,798,802,830,1001,1027,1198,1224,1255,1263,1269,1285,1307,1312,1317,1327,1336,1345,1349,1356,1375,1382,1383,1392,1395,1398,1402,1406,1410,1413,1414,1419,1424,1434,1439,1446,1452,1453,1456,1460,1465,1467,1469,1472,1475,1477,1481,1484,1491,1494,1497,1501,1503,1507,1509,1511,1513,1517,1525,1533,1545,1551,1560,1563,1574,1577,1578,1583,1584,1613,1682,1752,1753,1763,1772,1924,1926,1930,1933,1936,1939,1942,1945,1948,1951,1955,1958,1961,1964,1968,1971,1975,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,1999,2001,2003,2004,2005,2006,2007,2008,2009,2010,2012,2013,2015,2016,2018,2020,2021,2023,2024,2025,2026,2027,2028,2030,2031,2032,2033,2034,2046,2048,2050,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2064,2066,2067,2068,2069,2070,2071,2072,2074,2078,2094,2095,2096,2097,2098,2099,2103,2104,2105,2106,2108,2110,2112,2114,2116,2117,2118,2119,2121,2123,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2136,2139,2140,2141,2142,2144,2146,2147,2149,2150,2152,2154,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2167,2169,2170,2171,2172,2174,2175,2176,2177,2178,2180,2182,2184,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2253,2328,2331,2334,2337,2351,2368,2410,2439,2466,2475,2537,2905,2936,3074,3198,3222,3228,3257,3278,3402,3430,3436,3580,3606,3673,3744,3844,3864,3919,3931,3957", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "225,891,936,1983,2024,2079,2214,2278,2348,2409,2484,2560,2637,2875,2960,3042,3118,3194,3271,3349,3455,3561,3640,3969,4026,4886,4960,5035,5100,5166,5226,5287,5359,5432,5499,5567,5626,5685,5744,5803,5862,5916,5970,6023,6077,6131,6185,6440,6514,6593,6666,6740,6811,6883,6955,7028,7085,7143,7216,7290,7364,7439,7511,7584,7654,7725,7785,9598,9667,9736,9806,9880,9956,10020,10097,10173,10250,10315,10384,10461,10536,10605,10673,10750,10816,10877,10974,11039,11108,11207,11278,11337,11395,11452,11511,11575,11646,11718,11790,11862,11934,12001,12069,12137,12196,12259,12323,12413,12504,12564,12630,12697,12763,12833,12897,12950,13017,13078,13145,13258,13316,13379,13444,13509,13584,13657,13729,13773,13820,13866,13915,13976,14037,14098,14160,14224,14288,14352,14417,14480,14540,14601,14667,14726,14786,14848,14919,14979,15678,15764,16014,16104,16191,16279,16361,16444,16534,18259,18311,18369,18414,18480,18544,18601,18658,22267,22324,22372,22421,22676,23046,23093,23351,24522,24825,24889,24951,25011,25400,25474,25544,25622,25676,25746,25831,25879,25925,25986,26049,26115,26179,26250,26313,26378,26442,26503,26564,26616,26689,26763,26832,26907,26981,27055,27196,31519,32499,32577,32667,32755,32851,32941,33523,33612,33859,34140,34392,34677,35070,35547,35769,35991,36267,36494,36724,36954,37184,37414,37641,38060,38286,38711,38941,39369,39588,39871,40079,40210,40437,40863,41088,41515,41736,42161,42281,42557,42858,43182,43473,43787,43924,44055,44160,44402,44569,44773,44981,45252,45364,45476,45581,45698,45912,46058,46198,46284,46632,46720,46966,47384,47633,47715,47813,48470,48570,48822,49246,49501,49595,49684,49921,51945,52187,52289,52542,54698,65295,66811,77506,79034,80791,81417,81837,83098,84363,84619,84855,85402,85896,86501,86699,87279,88647,89022,89140,89678,89835,90031,90304,90560,90730,90871,90935,91300,91667,92343,92607,92945,93298,93392,93578,93884,94146,94271,94398,94637,94848,94967,95160,95337,95792,95973,96095,96354,96467,96654,96756,96863,96992,97267,97775,98271,99148,99442,100012,100161,100893,101065,101149,101485,101577,103643,108874,114245,114307,114885,115469,123416,123529,123758,123918,124070,124241,124407,124576,124743,124906,125149,125319,125492,125663,125937,126136,126341,126671,126755,126851,126947,127045,127145,127247,127349,127451,127553,127655,127755,127851,127963,128092,128215,128346,128477,128575,128689,128783,128923,129057,129153,129265,129365,129481,129577,129689,129789,129929,130065,130229,130359,130517,130667,130808,130952,131087,131199,131349,131477,131605,131741,131873,132003,132133,132245,133143,133289,133433,133571,133637,133727,133803,133907,133997,134099,134207,134315,134415,134495,134587,134685,134795,134847,134925,135031,135123,135227,135337,135459,135622,136412,136492,136592,136682,136792,136882,137123,137217,137323,137415,137515,137627,137741,137857,137973,138067,138181,138293,138395,138515,138637,138719,138823,138943,139069,139167,139261,139349,139461,139577,139699,139811,139986,140102,140188,140280,140392,140516,140583,140709,140777,140905,141049,141177,141246,141341,141456,141569,141668,141777,141888,141999,142100,142205,142305,142435,142526,142649,142743,142855,142941,143045,143141,143229,143347,143451,143555,143681,143769,143877,143977,144067,144177,144261,144363,144447,144501,144565,144671,144757,144867,144951,148045,150661,150779,150894,150974,151335,151921,153325,154669,156030,156418,159193,169431,170471,177284,181585,182336,182598,183445,183824,188102,188956,189185,193793,194803,196755,199155,203279,204023,206154,206494,207805", "endLines": "4,27,28,59,60,61,63,64,65,66,67,68,69,72,73,74,75,76,77,78,79,80,81,86,87,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,257,258,262,263,264,265,266,267,268,294,295,296,297,298,299,300,301,359,360,361,362,367,375,376,381,403,409,410,411,412,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,489,502,503,504,505,506,514,515,519,523,527,532,538,545,549,553,558,562,566,570,574,578,582,588,592,598,602,608,612,617,621,624,628,634,638,644,648,654,657,661,665,669,673,677,678,679,680,683,686,689,692,696,697,698,699,700,703,705,707,709,714,715,719,725,729,730,732,744,745,749,755,759,760,761,765,792,796,797,801,829,1000,1026,1197,1223,1254,1262,1268,1284,1306,1311,1316,1326,1335,1344,1348,1355,1374,1381,1382,1391,1394,1397,1401,1405,1409,1412,1413,1418,1423,1433,1438,1445,1451,1452,1455,1459,1464,1466,1468,1471,1474,1476,1480,1483,1490,1493,1496,1500,1502,1506,1508,1510,1512,1516,1524,1532,1544,1550,1559,1562,1573,1576,1577,1582,1583,1588,1681,1751,1752,1762,1771,1772,1925,1929,1932,1935,1938,1941,1944,1947,1950,1954,1957,1960,1963,1967,1970,1974,1978,1979,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1996,1997,1998,2000,2002,2003,2004,2005,2006,2007,2008,2009,2011,2012,2014,2015,2017,2019,2020,2022,2023,2024,2025,2026,2027,2029,2030,2031,2032,2033,2034,2047,2049,2051,2052,2053,2054,2055,2056,2057,2058,2059,2060,2061,2062,2063,2065,2066,2067,2068,2069,2070,2071,2073,2077,2081,2094,2095,2096,2097,2098,2102,2103,2104,2105,2107,2109,2111,2113,2115,2116,2117,2118,2120,2122,2124,2125,2126,2127,2128,2129,2130,2131,2132,2133,2134,2135,2138,2139,2140,2141,2143,2145,2146,2148,2149,2151,2153,2155,2156,2157,2158,2159,2160,2161,2162,2163,2164,2165,2166,2168,2169,2170,2171,2173,2174,2175,2176,2177,2179,2181,2183,2185,2186,2187,2188,2189,2190,2191,2192,2193,2194,2195,2196,2197,2198,2199,2200,2327,2330,2333,2336,2350,2356,2377,2438,2465,2474,2536,2899,2908,2963,3091,3221,3227,3233,3277,3401,3421,3435,3439,3585,3640,3684,3809,3863,3918,3930,3956,3963", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "275,931,980,2019,2074,2136,2273,2343,2404,2479,2555,2632,2710,2955,3037,3113,3189,3266,3344,3450,3556,3635,3715,4021,4079,4955,5030,5095,5161,5221,5282,5354,5427,5494,5562,5621,5680,5739,5798,5857,5911,5965,6018,6072,6126,6180,6234,6509,6588,6661,6735,6806,6878,6950,7023,7080,7138,7211,7285,7359,7434,7506,7579,7649,7720,7780,7841,9662,9731,9801,9875,9951,10015,10092,10168,10245,10310,10379,10456,10531,10600,10668,10745,10811,10872,10969,11034,11103,11202,11273,11332,11390,11447,11506,11570,11641,11713,11785,11857,11929,11996,12064,12132,12191,12254,12318,12408,12499,12559,12625,12692,12758,12828,12892,12945,13012,13073,13140,13253,13311,13374,13439,13504,13579,13652,13724,13768,13815,13861,13910,13971,14032,14093,14155,14219,14283,14347,14412,14475,14535,14596,14662,14721,14781,14843,14914,14974,15042,15759,15846,16099,16186,16274,16356,16439,16529,16620,18306,18364,18409,18475,18539,18596,18653,18707,22319,22367,22416,22467,22705,23088,23137,23392,24549,24884,24946,25006,25063,25469,25539,25617,25671,25741,25826,25874,25920,25981,26044,26110,26174,26245,26308,26373,26437,26498,26559,26611,26684,26758,26827,26902,26976,27050,27191,27261,31567,32572,32662,32750,32846,32936,33518,33607,33854,34135,34387,34672,35065,35542,35764,35986,36262,36489,36719,36949,37179,37409,37636,38055,38281,38706,38936,39364,39583,39866,40074,40205,40432,40858,41083,41510,41731,42156,42276,42552,42853,43177,43468,43782,43919,44050,44155,44397,44564,44768,44976,45247,45359,45471,45576,45693,45907,46053,46193,46279,46627,46715,46961,47379,47628,47710,47808,48465,48565,48817,49241,49496,49590,49679,49916,51940,52182,52284,52537,54693,65290,66806,77501,79029,80786,81412,81832,83093,84358,84614,84850,85397,85891,86496,86694,87274,88642,89017,89135,89673,89830,90026,90299,90555,90725,90866,90930,91295,91662,92338,92602,92940,93293,93387,93573,93879,94141,94266,94393,94632,94843,94962,95155,95332,95787,95968,96090,96349,96462,96649,96751,96858,96987,97262,97770,98266,99143,99437,100007,100156,100888,101060,101144,101480,101572,101850,108869,114240,114302,114880,115464,115555,123524,123753,123913,124065,124236,124402,124571,124738,124901,125144,125314,125487,125658,125932,126131,126336,126666,126750,126846,126942,127040,127140,127242,127344,127446,127548,127650,127750,127846,127958,128087,128210,128341,128472,128570,128684,128778,128918,129052,129148,129260,129360,129476,129572,129684,129784,129924,130060,130224,130354,130512,130662,130803,130947,131082,131194,131344,131472,131600,131736,131868,131998,132128,132240,132380,133284,133428,133566,133632,133722,133798,133902,133992,134094,134202,134310,134410,134490,134582,134680,134790,134842,134920,135026,135118,135222,135332,135454,135617,135774,136487,136587,136677,136787,136877,137118,137212,137318,137410,137510,137622,137736,137852,137968,138062,138176,138288,138390,138510,138632,138714,138818,138938,139064,139162,139256,139344,139456,139572,139694,139806,139981,140097,140183,140275,140387,140511,140578,140704,140772,140900,141044,141172,141241,141336,141451,141564,141663,141772,141883,141994,142095,142200,142300,142430,142521,142644,142738,142850,142936,143040,143136,143224,143342,143446,143550,143676,143764,143872,143972,144062,144172,144256,144358,144442,144496,144560,144666,144752,144862,144946,145066,150656,150774,150889,150969,151330,151563,152433,154664,156025,156413,159188,169241,169561,171823,177851,182331,182593,182793,183819,188097,188703,189180,189331,194003,195881,197062,202176,204018,206149,206489,207800,208003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\662b6f3f4294d98a11aa8ce24a3a2b88\\transformed\\transition-1.4.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,95,142,185,240,287,341,393,442,503", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "90,137,180,235,282,336,388,437,498,548"}, "to": {"startLines": "365,366,371,378,379,398,399,400,401,402", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "22589,22629,22846,23184,23239,24256,24310,24362,24411,24472", "endColumns": "39,46,42,54,46,53,51,48,60,49", "endOffsets": "22624,22671,22884,23234,23281,24305,24357,24406,24467,24517"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5ecd5a1cc2195013671502cd2470e636\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "82,83,84,85,248,249,474,476,477,478", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "3720,3778,3844,3907,15047,15118,30397,30522,30589,30668", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "3773,3839,3902,3964,15113,15185,30460,30584,30663,30732"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\52dfd6d7c21c1e36dbdf0d304093d642\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "407", "startColumns": "4", "startOffsets": "24711", "endColumns": "49", "endOffsets": "24756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3849d2a4662c79004aa48857ab62a419\\transformed\\recyclerview-1.0.0\\res\\values\\values.xml", "from": {"startLines": "30,31,32,33,34,35,36,2", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "1535,1594,1642,1698,1773,1849,1921,55", "endLines": "30,31,32,33,34,35,36,29", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "1589,1637,1693,1768,1844,1916,1982,1530"}, "to": {"startLines": "259,260,261,269,270,271,368,3586", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "15851,15910,15958,16625,16700,16776,22710,194008", "endLines": "259,260,261,269,270,271,368,3605", "endColumns": "58,47,55,74,75,71,65,24", "endOffsets": "15905,15953,16009,16695,16771,16843,22771,194798"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\84fd36235785b204f68ac36381954b4b\\transformed\\coordinatorlayout-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,102,3,13", "startColumns": "4,4,4,4", "startOffsets": "55,5935,116,724", "endLines": "2,104,12,101", "endColumns": "60,12,24,24", "endOffsets": "111,6075,719,5930"}, "to": {"startLines": "3,2203,2964,2970", "startColumns": "4,4,4,4", "startOffsets": "164,145210,171828,172039", "endLines": "3,2205,2969,3053", "endColumns": "60,12,24,24", "endOffsets": "220,145350,172034,176550"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\01880ab3b0e2d1af0a34ca552f67d88d\\transformed\\jetified-activity-1.9.3\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "377,405", "startColumns": "4,4", "startOffsets": "23142,24597", "endColumns": "41,59", "endOffsets": "23179,24652"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a49cd171355a6a39687e69f028c8bcbc\\transformed\\fragment-1.7.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "364,380,408,3150,3155", "startColumns": "4,4,4,4,4", "startOffsets": "22532,23286,24761,180417,180587", "endLines": "364,380,408,3154,3158", "endColumns": "56,64,63,24,24", "endOffsets": "22584,23346,24820,180582,180731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\917396157a8b62d1fd7b8337d11e2a37\\transformed\\jetified-ucrop-2.2.8\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,64,71,80,89,98,104,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,145,237,286,345,424,489,555,618,679,736,790,880,969,1057,1113,1184,1262,1311,1381,1458,1544,1632,1725,1807,1874,1940,2001,2086,2164,2229,2296,2354,2429,2490,2549,2624,2685,2745,2801,2852,2913,2977,3038,3097,3170,3239,3307,3351,3477,3539,3597,3646,3832,3880,3926,4285,4706,5252,5796,6248,6616,6877", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,63,70,79,88,97,103,111,133", "endColumns": "89,91,48,58,78,64,65,62,60,56,53,89,88,87,55,70,77,48,69,76,85,87,92,81,66,65,60,84,77,64,66,57,74,60,58,74,60,59,55,50,60,63,60,58,72,68,67,43,125,61,57,48,185,47,45,12,12,12,12,12,12,24,24", "endOffsets": "140,232,281,340,419,484,550,613,674,731,785,875,964,1052,1108,1179,1257,1306,1376,1453,1539,1627,1720,1802,1869,1935,1996,2081,2159,2224,2291,2349,2424,2485,2544,2619,2680,2740,2796,2847,2908,2972,3033,3092,3165,3234,3302,3346,3472,3534,3592,3641,3827,3875,3921,4280,4701,5247,5791,6243,6611,6872,7796"}, "to": {"startLines": "143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,417,492,493,494,495,496,497,498,499,2206,2213,2220,2229,2238,2247,3964,3972", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7846,7936,8028,8077,8136,8215,8280,8346,8409,8470,8527,8581,8671,8760,8848,8904,8975,9053,9102,9172,9249,9335,9423,9516,18712,18779,18845,18906,18991,19069,19134,19201,19259,19334,19395,19454,19529,19590,19650,19706,19757,19818,19882,19943,20002,20075,25332,31782,31826,31952,32014,32072,32121,32307,32355,145355,145714,146135,146681,147225,147677,208008,208269", "endLines": "143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,302,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,417,492,493,494,495,496,497,498,499,2212,2219,2228,2237,2246,2252,3971,3993", "endColumns": "89,91,48,58,78,64,65,62,60,56,53,89,88,87,55,70,77,48,69,76,85,87,92,81,66,65,60,84,77,64,66,57,74,60,58,74,60,59,55,50,60,63,60,58,72,68,67,43,125,61,57,48,185,47,45,12,12,12,12,12,12,24,24", "endOffsets": "7931,8023,8072,8131,8210,8275,8341,8404,8465,8522,8576,8666,8755,8843,8899,8970,9048,9097,9167,9244,9330,9418,9511,9593,18774,18840,18901,18986,19064,19129,19196,19254,19329,19390,19449,19524,19585,19645,19701,19752,19813,19877,19938,19997,20070,20139,25395,31821,31947,32009,32067,32116,32302,32350,32396,145709,146130,146676,147220,147672,148040,208264,209147"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\10d0d8c1f4c5c1b7a3b300d05f7ebb5e\\transformed\\preference-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,22,23,24,25,42,45,51,57,60,66,70,73,80,86,89,95,100,105,112,114,120,126,134,139,146,151,157,161,168,172,178,184,187,192,193,194,199,215,238,243,257,268,348,358,368,386,392,439,461,485", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,178,247,311,366,434,501,566,623,680,728,776,837,900,963,1001,1058,1102,1242,1381,1431,1479,2917,3022,3378,3716,3862,4202,4414,4577,4984,5322,5445,5784,6023,6280,6651,6711,7049,7335,7784,8076,8464,8769,9113,9358,9688,9895,10163,10436,10580,10949,10996,11052,11308,12367,13788,14126,15012,15622,20168,20687,21229,22503,22763,25467,26989,28470", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,21,22,23,24,41,44,50,56,59,65,69,72,79,85,88,94,99,104,111,113,119,125,133,138,145,150,156,160,167,171,177,183,186,191,192,193,198,214,237,242,256,267,347,357,367,385,391,438,460,484,508", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "173,242,306,361,429,496,561,618,675,723,771,832,895,958,996,1053,1097,1237,1376,1426,1474,2912,3017,3373,3711,3857,4197,4409,4572,4979,5317,5440,5779,6018,6275,6646,6706,7044,7330,7779,8071,8459,8764,9108,9353,9683,9890,10158,10431,10575,10944,10991,11047,11303,12362,13783,14121,15007,15617,20163,20682,21224,22498,22758,25462,26984,28465,29984"}, "to": {"startLines": "62,122,287,288,289,290,291,292,293,372,373,374,414,415,473,475,484,485,491,500,501,1589,1773,1776,1782,1788,1791,1797,1801,1804,1811,1817,1820,1826,1831,1836,1843,1845,1851,1857,1865,1870,1877,1882,1888,1892,1899,1903,1909,1915,1918,1922,1923,2900,2915,3054,3092,3234,3422,3440,3504,3514,3524,3531,3537,3641,3810,3827", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2141,6371,17826,17890,17945,18013,18080,18145,18202,22889,22937,22985,25136,25199,30359,30465,31259,31303,31643,32401,32451,101855,115560,115665,115910,116248,116394,116734,116946,117109,117516,117854,117977,118316,118555,118812,119183,119243,119581,119867,120316,120608,120996,121301,121645,121890,122220,122427,122695,122968,123112,123313,123360,169246,169769,176555,177856,182798,188708,189336,191261,191543,191848,192110,192370,195886,202181,202711", "endLines": "62,122,287,288,289,290,291,292,293,372,373,374,414,415,473,475,484,487,491,500,501,1605,1775,1781,1787,1790,1796,1800,1803,1810,1816,1819,1825,1830,1835,1842,1844,1850,1856,1864,1869,1876,1881,1887,1891,1898,1902,1908,1914,1917,1921,1922,1923,2904,2925,3073,3095,3243,3429,3503,3513,3523,3530,3536,3579,3653,3826,3843", "endColumns": "72,68,63,54,67,66,64,56,56,47,47,60,62,62,37,56,43,13,138,49,47,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,46,55,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "2209,6435,17885,17940,18008,18075,18140,18197,18254,22932,22980,23041,25194,25257,30392,30517,31298,31438,31777,32446,32494,103288,115660,115905,116243,116389,116729,116941,117104,117511,117849,117972,118311,118550,118807,119178,119238,119576,119862,120311,120603,120991,121296,121640,121885,122215,122422,122690,122963,123107,123308,123355,123411,169426,170165,177279,178000,183125,188951,191256,191538,191843,192105,192365,193788,196333,202706,203274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8470c89ba64407e2dd527ce11792b982\\transformed\\jetified-play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "90,91,92,93,94,95,96,97,455,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,3244,3654", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4215,4305,4385,4475,4565,4645,4726,4806,28026,28131,28312,28437,28544,28724,28847,28963,29233,29421,29526,29707,29832,30007,30155,30218,30280,183130,196338", "endLines": "90,91,92,93,94,95,96,97,455,456,457,458,459,460,461,462,464,465,466,467,468,469,470,471,472,3256,3672", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "4300,4380,4470,4560,4640,4721,4801,4881,28126,28307,28432,28539,28719,28842,28958,29061,29416,29521,29702,29827,30002,30150,30213,30275,30354,183440,196750"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\bb4aceea626ef6ec2db1cf5b297099f2\\transformed\\jetified-play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "413,463", "startColumns": "4,4", "startOffsets": "25068,29066", "endColumns": "67,166", "endOffsets": "25131,29228"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3e108fe9d597d03770ae82acaebb5b63\\transformed\\jetified-window-1.2.0\\res\\values\\values.xml", "from": {"startLines": "2,3,9,17,25,37,43,49,50,51,52,53,54,55,61,66,74,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,114,287,506,725,1039,1227,1414,1467,1527,1579,1624,1663,1723,1918,2076,2358,2972", "endLines": "2,8,16,24,36,42,48,49,50,51,52,53,54,60,65,73,88,104", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "109,282,501,720,1034,1222,1409,1462,1522,1574,1619,1658,1718,1913,2071,2353,2967,3621"}, "to": {"startLines": "2,5,11,19,30,42,48,54,55,56,57,58,363,2357,2363,3685,3693,3708", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,280,453,672,1045,1359,1547,1734,1787,1847,1899,1944,22472,151568,151763,197067,197349,197963", "endLines": "2,10,18,26,41,47,53,54,55,56,57,58,363,2362,2367,3692,3707,3723", "endColumns": "58,11,11,11,11,11,11,52,59,51,44,38,59,24,24,24,24,24", "endOffsets": "159,448,667,886,1354,1542,1729,1782,1842,1894,1939,1978,22527,151758,151916,197344,197958,198612"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\24c1e520e2b1e7ad04d8500a33fb9ad9\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "445,446", "startColumns": "4,4", "startOffsets": "27266,27348", "endColumns": "81,83", "endOffsets": "27343,27427"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\010887bb3359843376c02ecf4bf2ef43\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "406", "startColumns": "4", "startOffsets": "24657", "endColumns": "53", "endOffsets": "24706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\53f114d110e356c36b5f50ab357f361f\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "447", "startColumns": "4", "startOffsets": "27432", "endColumns": "82", "endOffsets": "27510"}}]}]}
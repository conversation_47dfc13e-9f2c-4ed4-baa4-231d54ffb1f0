import 'package:flutter/material.dart';
import 'profile_complete_page.dart';
import '../../data/services/profile_service.dart';

class PrivacySettingsPage extends StatefulWidget {
  const PrivacySettingsPage({super.key});

  @override
  State<PrivacySettingsPage> createState() => _PrivacySettingsPageState();
}

class _PrivacySettingsPageState extends State<PrivacySettingsPage> {
  bool _blurPhotos = false;
  bool _hideAge = false;
  bool _hideDistance = false;
  bool _hideLastSeen = false;
  bool _onlyMatchedCanMessage = true;
  bool _showOnlineStatus = true;
  bool _isLoading = false;

  void _completeProfileSetup() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Save privacy settings to Firebase
      await ProfileService().updatePrivacySettings(
        blurPhotos: _blurPhotos,
        hideAge: _hideAge,
        hideDistance: _hideDistance,
        hideLastSeen: _hideLastSeen,
        onlyMatchedCanMessage: _onlyMatchedCanMessage,
        showOnlineStatus: _showOnlineStatus,
      );

      // Complete profile setup
      await ProfileService().completeProfileSetup();

      if (mounted) {
        // Navigate to main app or profile complete page
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const ProfileCompleteePage(),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Privacy Settings'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: const Color(0xFFE74C3C),
        actions: [
          TextButton(
            onPressed: () {
              // Skip this step and complete setup
              _completeProfileSetup();
            },
            child: const Text(
              'Skip',
              style: TextStyle(
                color: Color(0xFFE74C3C),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Progress indicator
              LinearProgressIndicator(
                value: 1.0, // 4/4 steps completed
                backgroundColor: Colors.grey[300],
                valueColor:
                    const AlwaysStoppedAnimation<Color>(Color(0xFFE74C3C)),
              ),

              const SizedBox(height: 32),

              Text(
                'Privacy & Safety',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
              ),

              const SizedBox(height: 8),

              Text(
                'Control who can see your information and how you appear to others.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
              ),

              const SizedBox(height: 32),

              // Photo Privacy
              _buildSectionHeader('Photo Privacy'),

              _buildPrivacyTile(
                title: 'Blur Photos',
                subtitle:
                    'Your photos will be blurred until you match with someone',
                value: _blurPhotos,
                onChanged: (value) => setState(() => _blurPhotos = value),
                icon: Icons.blur_on,
                isRecommended: true,
              ),

              const SizedBox(height: 24),

              // Profile Information
              _buildSectionHeader('Profile Information'),

              _buildPrivacyTile(
                title: 'Hide Age',
                subtitle: 'Your age won\'t be shown on your profile',
                value: _hideAge,
                onChanged: (value) => setState(() => _hideAge = value),
                icon: Icons.cake,
              ),

              _buildPrivacyTile(
                title: 'Hide Distance',
                subtitle: 'Others won\'t see how far away you are',
                value: _hideDistance,
                onChanged: (value) => setState(() => _hideDistance = value),
                icon: Icons.location_off,
              ),

              const SizedBox(height: 24),

              // Activity Privacy
              _buildSectionHeader('Activity Privacy'),

              _buildPrivacyTile(
                title: 'Hide Last Seen',
                subtitle: 'Others won\'t see when you were last active',
                value: _hideLastSeen,
                onChanged: (value) => setState(() => _hideLastSeen = value),
                icon: Icons.visibility_off,
              ),

              _buildPrivacyTile(
                title: 'Show Online Status',
                subtitle: 'Let others know when you\'re online',
                value: _showOnlineStatus,
                onChanged: (value) => setState(() => _showOnlineStatus = value),
                icon: Icons.circle,
              ),

              const SizedBox(height: 24),

              // Messaging Privacy
              _buildSectionHeader('Messaging'),

              _buildPrivacyTile(
                title: 'Only Matched Users Can Message',
                subtitle:
                    'Only people you\'ve matched with can send you messages',
                value: _onlyMatchedCanMessage,
                onChanged: (value) =>
                    setState(() => _onlyMatchedCanMessage = value),
                icon: Icons.message,
                isRecommended: true,
              ),

              const SizedBox(height: 32),

              // Cultural note
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFE74C3C).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: const Color(0xFFE74C3C).withOpacity(0.3),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(
                      Icons.security,
                      color: Color(0xFFE74C3C),
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'We recommend enabling photo blur and message restrictions for enhanced privacy and safety.',
                        style: TextStyle(
                          color: const Color(0xFFE74C3C).withOpacity(0.8),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Complete Setup Button
              ElevatedButton(
                onPressed: _isLoading ? null : _completeProfileSetup,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFFE74C3C),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'Complete Profile Setup',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildPrivacyTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    required IconData icon,
    bool isRecommended = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isRecommended
            ? Border.all(color: const Color(0xFFE74C3C).withOpacity(0.3))
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SwitchListTile(
        title: Row(
          children: [
            Icon(
              icon,
              color: const Color(0xFFE74C3C),
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
            if (isRecommended)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFE74C3C),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Text(
                  'Recommended',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
          ],
        ),
        subtitle: Padding(
          padding: const EdgeInsets.only(left: 32),
          child: Text(
            subtitle,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ),
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFFE74C3C),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      ),
    );
  }
}
